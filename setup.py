"""
Setup script for Car Showroom Management System
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="car-showroom-system",
    version="1.0.0",
    author="Car Showroom Team",
    author_email="<EMAIL>",
    description="نظام إدارة معرض السيارات المتكامل",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-repo/car-showroom-system",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business",
        "Topic :: Database :: Front-Ends",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    entry_points={
        "console_scripts": [
            "car-showroom=app.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.kv", "*.png", "*.jpg", "*.ico"],
    },
    keywords="car showroom management system inventory sales customers",
    project_urls={
        "Bug Reports": "https://github.com/your-repo/car-showroom-system/issues",
        "Source": "https://github.com/your-repo/car-showroom-system",
        "Documentation": "https://github.com/your-repo/car-showroom-system/wiki",
    },
)
