"""
Simple test script for Car Showroom Management System
سكريبت اختبار بسيط لنظام إدارة معرض السيارات
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test importing all main modules"""
    print("🧪 Testing module imports...")
    
    try:
        # Test database models
        from models.database import init_database, get_session
        from models.user import User, UserRole
        from models.car import Car, CarStatus, FuelType, Transmission
        from models.customer import Customer
        from models.sale import Sale, PaymentMethod, SaleStatus
        from models.maintenance import Maintenance, MaintenanceType, MaintenanceStatus
        print("✅ Database models imported successfully")
        
        # Test services
        from services.auth_service import auth_service
        from services.car_service import car_service
        from services.customer_service import customer_service
        print("✅ Services imported successfully")
        
        # Test core modules
        from core.config import APP_NAME, APP_VERSION
        from core.utils import generate_unique_id, format_currency
        print("✅ Core modules imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_database():
    """Test database initialization"""
    print("\n🗄️  Testing database...")
    
    try:
        from models.database import init_database, get_session
        
        # Initialize database
        init_database()
        print("✅ Database initialized successfully")
        
        # Test session
        session = get_session()
        session.close()
        print("✅ Database session works")
        
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_auth_service():
    """Test authentication service"""
    print("\n🔐 Testing authentication service...")
    
    try:
        from services.auth_service import auth_service
        from models.user import UserRole
        
        # Test password hashing
        password = "test123"
        hashed = auth_service.hash_password(password)
        is_valid = auth_service.verify_password(password, hashed)
        
        if is_valid:
            print("✅ Password hashing works")
        else:
            print("❌ Password hashing failed")
            return False
        
        # Test user creation
        result = auth_service.create_user(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User",
            password="test123",
            role=UserRole.EMPLOYEE
        )
        
        if result['success']:
            print("✅ User creation works")
        else:
            print(f"⚠️  User creation: {result['message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Auth service error: {e}")
        return False

def test_car_service():
    """Test car service"""
    print("\n🚗 Testing car service...")
    
    try:
        from services.car_service import car_service
        from models.car import FuelType, Transmission
        
        # Test adding a car
        car_data = {
            'vin': 'TEST12345678901234',
            'make': 'Toyota',
            'model': 'Camry',
            'year': 2023,
            'color': 'White',
            'fuel_type': FuelType.GASOLINE.value,
            'transmission': Transmission.AUTOMATIC.value,
            'purchase_price': 25000,
            'selling_price': 30000
        }
        
        result = car_service.add_car(car_data)
        
        if result['success']:
            print("✅ Car service works")
        else:
            print(f"⚠️  Car service: {result['message']}")
        
        # Test getting inventory stats
        stats = car_service.get_inventory_stats()
        print(f"✅ Inventory stats: {stats['total_cars']} cars")
        
        return True
        
    except Exception as e:
        print(f"❌ Car service error: {e}")
        return False

def test_customer_service():
    """Test customer service"""
    print("\n👥 Testing customer service...")
    
    try:
        from services.customer_service import customer_service
        
        # Test adding a customer
        customer_data = {
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'phone': '+1234567890',
            'address': '123 Main St',
            'city': 'Anytown',
            'state': 'CA',
            'zip_code': '12345'
        }
        
        result = customer_service.add_customer(customer_data)
        
        if result['success']:
            print("✅ Customer service works")
        else:
            print(f"⚠️  Customer service: {result['message']}")
        
        # Test getting customer stats
        stats = customer_service.get_customer_stats()
        print(f"✅ Customer stats: {stats['total_customers']} customers")
        
        return True
        
    except Exception as e:
        print(f"❌ Customer service error: {e}")
        return False

def test_ui_components():
    """Test UI components (without actually running the app)"""
    print("\n🎨 Testing UI components...")
    
    try:
        # Test KivyMD imports
        from kivymd.app import MDApp
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.list import MDListItem
        print("✅ KivyMD components imported successfully")
        
        # Test if main.kv file exists and is readable
        if os.path.exists('ui/main.kv'):
            with open('ui/main.kv', 'r', encoding='utf-8') as f:
                content = f.read()
                if 'MDNavigationLayout' in content:
                    print("✅ UI layout file is valid")
                else:
                    print("⚠️  UI layout file may have issues")
        else:
            print("❌ UI layout file not found")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ UI component error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚗 Car Showroom Management System - System Test")
    print("=" * 60)
    
    tests = [
        ("Module Imports", test_imports),
        ("Database", test_database),
        ("Authentication Service", test_auth_service),
        ("Car Service", test_car_service),
        ("Customer Service", test_customer_service),
        ("UI Components", test_ui_components),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to use.")
        print("\n📋 Next steps:")
        print("   1. Run: python run.py")
        print("   2. Login with: admin / admin123")
        print("   3. Start managing your car showroom!")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        print("   Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
