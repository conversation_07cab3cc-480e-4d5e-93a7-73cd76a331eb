# 🚀 Quick Start Guide - دليل البدء السريع

## نظام إدارة معرض السيارات - Car Showroom Management System

### ⚡ التثبيت السريع - Quick Installation

#### الطريقة الأولى: التثبيت التلقائي
```bash
# تحميل المشروع
git clone https://github.com/your-repo/car-showroom-system.git
cd car-showroom-system

# تشغيل سكريبت التثبيت التلقائي
python install.py

# تشغيل النظام
python run.py
```

#### الطريقة الثانية: التثبيت اليدوي
```bash
# إنشاء البيئة الافتراضية
python -m venv .venv

# تفعيل البيئة الافتراضية
# Windows:
.venv\Scripts\activate
# macOS/Linux:
source .venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل النظام
python app/main.py
```

### 🔐 بيانات الدخول الافتراضية

```
اسم المستخدم: admin
كلمة المرور: admin123
```

⚠️ **مهم:** غيّر كلمة المرور فور تسجيل الدخول الأول!

### 🎯 الخطوات الأولى

#### 1. تسجيل الدخول
- افتح التطبيق
- أدخل بيانات الدخول الافتراضية
- اضغط "Login"

#### 2. إضافة أول سيارة
- اذهب إلى "Cars" من القائمة الجانبية
- اضغط على زر "+" في الأعلى
- املأ بيانات السيارة
- احفظ البيانات

#### 3. إضافة أول عميل
- اذهب إلى "Customers"
- اضغط على زر "+"
- أدخل معلومات العميل
- احفظ البيانات

#### 4. تسجيل أول عملية بيع
- اذهب إلى "Sales"
- اضغط على زر "+"
- اختر السيارة والعميل
- أدخل تفاصيل البيع
- احفظ العملية

### 📊 استكشاف الميزات

#### لوحة التحكم
- عرض الإحصائيات السريعة
- بطاقات الوصول السريع
- مراقبة الأداء

#### إدارة المخزون
- إضافة وتعديل السيارات
- تتبع حالة السيارات
- البحث والفلترة

#### إدارة العملاء
- قاعدة بيانات العملاء
- تاريخ المشتريات
- معلومات الاتصال

#### إدارة المبيعات
- تسجيل المبيعات
- طرق الدفع المختلفة
- حساب الضرائب والخصومات

#### إدارة الصيانة
- جدولة الصيانة
- تتبع أوامر العمل
- إدارة التكاليف

#### التقارير
- تقارير المبيعات
- تقارير المخزون
- تحليلات العملاء

### 🛠️ إعدادات النظام

#### تغيير كلمة المرور
1. سجل دخول كمدير
2. اذهب إلى إعدادات المستخدم
3. اختر "تغيير كلمة المرور"
4. أدخل كلمة المرور الجديدة

#### إضافة مستخدمين جدد
1. من شاشة تسجيل الدخول
2. اضغط "Create New Account"
3. املأ البيانات المطلوبة
4. اختر الدور المناسب

#### تخصيص الواجهة
- يمكن تغيير الثيم في `core/config.py`
- تخصيص الألوان والخطوط
- إضافة شعار الشركة

### 🔧 استكشاف الأخطاء

#### مشاكل شائعة وحلولها

**خطأ في تشغيل التطبيق:**
```bash
# تأكد من تثبيت المتطلبات
pip install -r requirements.txt

# تحقق من إصدار Python
python --version  # يجب أن يكون 3.8 أو أحدث
```

**خطأ في قاعدة البيانات:**
```bash
# احذف قاعدة البيانات وأعد إنشاءها
rm car_showroom.db
python app/main.py
```

**مشاكل في الواجهة:**
```bash
# تأكد من تثبيت KivyMD
pip install kivymd

# تحديث المكتبات
pip install --upgrade kivy kivymd
```

### 📱 اختبار النظام

```bash
# تشغيل اختبارات النظام
python test_system.py

# اختبار الوحدات الفردية
python -m pytest tests/  # إذا كانت متوفرة
```

### 📦 إنشاء ملف تنفيذي

```bash
# إنشاء ملف EXE للتوزيع
python build.py

# الملف سيكون في مجلد dist/
```

### 🎓 نصائح للاستخدام الأمثل

#### للمديرين
- راجع التقارير يومياً
- تابع مستويات المخزون
- راقب أداء المبيعات

#### لموظفي المبيعات
- حدّث بيانات العملاء باستمرار
- سجل جميع المبيعات فوراً
- استخدم ميزة البحث للعثور على السيارات

#### لفنيي الصيانة
- جدول الصيانة الدورية
- سجل جميع الأعمال المنجزة
- تابع قطع الغيار المطلوبة

### 📞 الحصول على المساعدة

#### الدعم الفني
- البريد الإلكتروني: <EMAIL>
- GitHub Issues: [رابط المشروع]
- الوثائق: README.md

#### الموارد المفيدة
- دليل المستخدم الكامل: README.md
- سجل التغييرات: CHANGELOG.md
- أمثلة الاستخدام: examples/ (إن وجد)

### 🔄 التحديثات

```bash
# تحديث النظام
git pull origin main
pip install -r requirements.txt --upgrade
```

### 🎉 مبروك!

أنت الآن جاهز لاستخدام نظام إدارة معرض السيارات!

**استمتع بإدارة معرضك بكفاءة وسهولة** 🚗✨

---

**للمزيد من المعلومات، راجع الملفات التالية:**
- `README.md` - الدليل الشامل
- `CHANGELOG.md` - سجل التحديثات
- `LICENSE` - رخصة الاستخدام
