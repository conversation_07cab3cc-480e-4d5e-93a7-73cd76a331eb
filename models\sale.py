"""
Sale model for sales transaction management
"""

from sqlalchemy import Column, Integer, String, DateTime, Float, Text, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base
import enum

class PaymentMethod(enum.Enum):
    CASH = "cash"
    CREDIT_CARD = "credit_card"
    BANK_TRANSFER = "bank_transfer"
    FINANCING = "financing"
    CHECK = "check"

class SaleStatus(enum.Enum):
    PENDING = "pending"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"

class Sale(Base):
    __tablename__ = "sales"
    
    id = Column(Integer, primary_key=True, index=True)
    sale_number = Column(String(20), unique=True, index=True, nullable=False)
    car_id = Column(Integer, ForeignKey("cars.id"), nullable=False)
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=False)
    salesperson_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Sale details
    sale_price = Column(Float, nullable=False)
    discount = Column(Float, default=0)
    tax_amount = Column(Float, default=0)
    total_amount = Column(Float, nullable=False)
    
    # Payment information
    payment_method = Column(Enum(PaymentMethod), nullable=False)
    down_payment = Column(Float, default=0)
    financing_amount = Column(Float, default=0)
    monthly_payment = Column(Float, default=0)
    loan_term_months = Column(Integer, default=0)
    
    # Status and dates
    status = Column(Enum(SaleStatus), default=SaleStatus.PENDING, nullable=False)
    sale_date = Column(DateTime(timezone=True), server_default=func.now())
    delivery_date = Column(DateTime(timezone=True))
    
    # Additional information
    notes = Column(Text)
    warranty_info = Column(Text)
    trade_in_value = Column(Float, default=0)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    car = relationship("Car", backref="sales")
    customer = relationship("Customer", backref="purchases")
    salesperson = relationship("User", backref="sales")
    
    def __repr__(self):
        return f"<Sale(number='{self.sale_number}', total=${self.total_amount})>"
    
    @property
    def final_amount(self):
        return self.sale_price - self.discount + self.tax_amount - self.trade_in_value
    
    @property
    def profit(self):
        if hasattr(self.car, 'purchase_price') and self.car.purchase_price:
            return self.sale_price - self.car.purchase_price - self.discount
        return 0
    
    def to_dict(self):
        return {
            'id': self.id,
            'sale_number': self.sale_number,
            'car_id': self.car_id,
            'customer_id': self.customer_id,
            'salesperson_id': self.salesperson_id,
            'sale_price': self.sale_price,
            'discount': self.discount,
            'tax_amount': self.tax_amount,
            'total_amount': self.total_amount,
            'payment_method': self.payment_method.value,
            'down_payment': self.down_payment,
            'financing_amount': self.financing_amount,
            'monthly_payment': self.monthly_payment,
            'loan_term_months': self.loan_term_months,
            'status': self.status.value,
            'sale_date': self.sale_date.isoformat() if self.sale_date else None,
            'delivery_date': self.delivery_date.isoformat() if self.delivery_date else None,
            'notes': self.notes,
            'warranty_info': self.warranty_info,
            'trade_in_value': self.trade_in_value,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
