
MDNavigationLayout:
    MDScreenManager:
        id: screen_manager

        # Login Screen - Luxury Design
        MDScreen:
            name: "login"
            md_bg_color: "#F8F9FA"

            # Background gradient effect
            canvas.before:
                Color:
                    rgba: 0.96, 0.97, 0.98, 1
                Rectangle:
                    pos: self.pos
                    size: self.size
                Color:
                    rgba: 0.85, 0.90, 0.95, 0.3
                Ellipse:
                    pos: self.width * 0.7, self.height * 0.8
                    size: self.width * 0.6, self.height * 0.6
                Color:
                    rgba: 0.75, 0.85, 0.95, 0.2
                Ellipse:
                    pos: -self.width * 0.2, -self.height * 0.2
                    size: self.width * 0.8, self.height * 0.8

            MDCard:
                size_hint: None, None
                size: "450dp", "600dp"
                pos_hint: {"center_x": 0.5, "center_y": 0.5}
                elevation: 20
                shadow_radius: 30
                shadow_color: [0.2, 0.2, 0.2, 0.3]
                padding: "40dp"
                spacing: "25dp"
                orientation: "vertical"
                md_bg_color: "#FFFFFF"
                radius: [25, 25, 25, 25]

                # Logo and Title Section
                MDBoxLayout:
                    orientation: "vertical"
                    spacing: "10dp"
                    size_hint_y: None
                    height: "120dp"

                    MDIcon:
                        icon: "car-luxury"
                        size_hint_y: None
                        height: "60dp"
                        theme_icon_color: "Primary"
                        halign: "center"

                    MDLabel:
                        text: "Car Showroom Elite"
                        role: "large"
                        halign: "center"
                        theme_text_color: "Primary"
                        font_style: "Display"

                    MDLabel:
                        text: "Luxury Management System"
                        role: "medium"
                        halign: "center"
                        theme_text_color: "Secondary"
                        font_style: "Title"

                # Elegant Divider
                MDDivider:
                    height: "2dp"
                    md_bg_color: app.theme_cls.primaryColor

                Widget:
                    size_hint_y: None
                    height: "30dp"

                # Premium Input Fields
                MDTextField:
                    id: username_field
                    hint_text: "Username"
                    icon_left: "account-circle"
                    mode: "outlined"
                    line_color_focus: app.theme_cls.primaryColor
                    text_color_focus: app.theme_cls.primaryColor
                    radius: [15, 15, 15, 15]
                    size_hint_y: None
                    height: "56dp"

                MDTextField:
                    id: password_field
                    hint_text: "Password"
                    icon_left: "lock-outline"
                    password: True
                    mode: "outlined"
                    line_color_focus: app.theme_cls.primaryColor
                    text_color_focus: app.theme_cls.primaryColor
                    radius: [15, 15, 15, 15]
                    size_hint_y: None
                    height: "56dp"

                Widget:
                    size_hint_y: None
                    height: "20dp"

                # Premium Login Button
                MDButton:
                    style: "filled"
                    theme_bg_color: "Primary"
                    size_hint_y: None
                    height: "56dp"
                    radius: [28, 28, 28, 28]
                    elevation: 8
                    on_release: app.login()

                    MDButtonText:
                        text: "Sign In"
                        font_style: "Title"

                Widget:
                    size_hint_y: None
                    height: "10dp"

                # Secondary Action Button
                MDButton:
                    style: "outlined"
                    theme_bg_color: "Primary"
                    size_hint_y: None
                    height: "48dp"
                    radius: [24, 24, 24, 24]
                    on_release: app.show_register_dialog()

                    MDButtonText:
                        text: "Create New Account"
                        theme_text_color: "Primary"

        # Main Dashboard Screen - Luxury Design
        MDScreen:
            name: "dashboard"
            md_bg_color: "#F8F9FA"

            # Elegant gradient background
            canvas.before:
                Color:
                    rgba: 0.98, 0.99, 1, 1
                Rectangle:
                    pos: self.pos
                    size: self.size
                Color:
                    rgba: 0.9, 0.95, 1, 0.3
                Ellipse:
                    pos: self.width * 0.8, self.height * 0.9
                    size: self.width * 0.4, self.height * 0.4

            MDTopAppBar:
                id: top_app_bar
                pos_hint: {"top": 1}
                elevation: 12
                shadow_radius: 20
                title: "Elite Dashboard"
                md_bg_color: "#FFFFFF"
                specific_text_color: app.theme_cls.primaryColor
                left_action_items: [["menu", lambda x: nav_drawer.set_state("open")]]
                right_action_items: [["account-circle", lambda x: app.show_profile()], ["logout", lambda x: app.logout()]]

            MDBoxLayout:
                orientation: "vertical"
                padding: "20dp"
                spacing: "20dp"
                pos_hint: {"top": 0.9}

                # Premium Statistics Cards
                MDLabel:
                    text: "Business Overview"
                    role: "large"
                    theme_text_color: "Primary"
                    size_hint_y: None
                    height: "40dp"
                    font_style: "Headline"

                MDGridLayout:
                    cols: 2
                    spacing: "20dp"
                    size_hint_y: None
                    height: "220dp"

                    # Luxury Car Stats Card
                    MDCard:
                        elevation: 15
                        shadow_radius: 25
                        shadow_color: [0.2, 0.4, 0.8, 0.2]
                        padding: "25dp"
                        radius: [20, 20, 20, 20]
                        md_bg_color: "#E3F2FD"

                        canvas.before:
                            Color:
                                rgba: 0.1, 0.3, 0.8, 0.1
                            RoundedRectangle:
                                pos: self.pos
                                size: self.size
                                radius: [20, 20, 20, 20]

                        MDBoxLayout:
                            orientation: "vertical"
                            spacing: "10dp"

                            MDIcon:
                                icon: "car-luxury"
                                size_hint_y: None
                                height: "56dp"
                                theme_icon_color: "Custom"
                                icon_color: "#1976D2"
                                halign: "center"

                            MDLabel:
                                text: "Total Cars"
                                role: "medium"
                                theme_text_color: "Custom"
                                text_color: "#1976D2"
                                halign: "center"
                                font_style: "Title"

                            MDLabel:
                                id: total_cars_label
                                text: "0"
                                role: "large"
                                theme_text_color: "Custom"
                                text_color: "#0D47A1"
                                halign: "center"
                                font_style: "Display"

                    # Luxury Customer Stats Card
                    MDCard:
                        elevation: 15
                        shadow_radius: 25
                        shadow_color: [0.8, 0.2, 0.4, 0.2]
                        padding: "25dp"
                        radius: [20, 20, 20, 20]
                        md_bg_color: "#FCE4EC"

                        canvas.before:
                            Color:
                                rgba: 0.8, 0.1, 0.3, 0.1
                            RoundedRectangle:
                                pos: self.pos
                                size: self.size
                                radius: [20, 20, 20, 20]

                        MDBoxLayout:
                            orientation: "vertical"
                            spacing: "10dp"

                            MDIcon:
                                icon: "account-star"
                                size_hint_y: None
                                height: "56dp"
                                theme_icon_color: "Custom"
                                icon_color: "#C2185B"
                                halign: "center"

                            MDLabel:
                                text: "VIP Customers"
                                role: "medium"
                                theme_text_color: "Custom"
                                text_color: "#C2185B"
                                halign: "center"
                                font_style: "Title"

                            MDLabel:
                                id: total_customers_label
                                text: "0"
                                role: "large"
                                theme_text_color: "Custom"
                                text_color: "#880E4F"
                                halign: "center"
                                font_style: "Display"

                    # Luxury Sales Stats Card
                    MDCard:
                        elevation: 15
                        shadow_radius: 25
                        shadow_color: [0.2, 0.8, 0.4, 0.2]
                        padding: "25dp"
                        radius: [20, 20, 20, 20]
                        md_bg_color: "#E8F5E8"

                        canvas.before:
                            Color:
                                rgba: 0.1, 0.8, 0.3, 0.1
                            RoundedRectangle:
                                pos: self.pos
                                size: self.size
                                radius: [20, 20, 20, 20]

                        MDBoxLayout:
                            orientation: "vertical"
                            spacing: "10dp"

                            MDIcon:
                                icon: "chart-line-variant"
                                size_hint_y: None
                                height: "56dp"
                                theme_icon_color: "Custom"
                                icon_color: "#388E3C"
                                halign: "center"

                            MDLabel:
                                text: "Premium Sales"
                                role: "medium"
                                theme_text_color: "Custom"
                                text_color: "#388E3C"
                                halign: "center"
                                font_style: "Title"

                            MDLabel:
                                id: total_sales_label
                                text: "0"
                                role: "large"
                                theme_text_color: "Custom"
                                text_color: "#1B5E20"
                                halign: "center"
                                font_style: "Display"

                    # Luxury Revenue Stats Card
                    MDCard:
                        elevation: 15
                        shadow_radius: 25
                        shadow_color: [0.8, 0.6, 0.2, 0.2]
                        padding: "25dp"
                        radius: [20, 20, 20, 20]
                        md_bg_color: "#FFF8E1"

                        canvas.before:
                            Color:
                                rgba: 0.8, 0.6, 0.1, 0.1
                            RoundedRectangle:
                                pos: self.pos
                                size: self.size
                                radius: [20, 20, 20, 20]

                        MDBoxLayout:
                            orientation: "vertical"
                            spacing: "10dp"

                            MDIcon:
                                icon: "currency-usd-circle"
                                size_hint_y: None
                                height: "56dp"
                                theme_icon_color: "Custom"
                                icon_color: "#F57C00"
                                halign: "center"

                            MDLabel:
                                text: "Total Revenue"
                                role: "medium"
                                theme_text_color: "Custom"
                                text_color: "#F57C00"
                                halign: "center"
                                font_style: "Title"

                            MDLabel:
                                id: total_revenue_label
                                text: "$0"
                                role: "large"
                                theme_text_color: "Custom"
                                text_color: "#E65100"
                                halign: "center"
                                font_style: "Display"

                # Premium Quick Actions
                Widget:
                    size_hint_y: None
                    height: "20dp"

                MDLabel:
                    text: "Premium Actions"
                    role: "large"
                    theme_text_color: "Primary"
                    size_hint_y: None
                    height: "40dp"
                    font_style: "Headline"

                MDGridLayout:
                    cols: 3
                    spacing: "20dp"
                    size_hint_y: None
                    height: "140dp"

                    # Luxury Add Car Card
                    MDCard:
                        elevation: 12
                        shadow_radius: 20
                        shadow_color: [0.2, 0.4, 0.8, 0.3]
                        radius: [18, 18, 18, 18]
                        md_bg_color: "#FFFFFF"
                        on_release: app.navigate_to("cars")

                        MDBoxLayout:
                            orientation: "vertical"
                            padding: "20dp"
                            spacing: "12dp"

                            MDIcon:
                                icon: "car-sports"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Custom"
                                icon_color: "#1976D2"
                                halign: "center"

                            MDLabel:
                                text: "Luxury Cars"
                                role: "medium"
                                theme_text_color: "Custom"
                                text_color: "#1976D2"
                                halign: "center"
                                font_style: "Title"

                    # Luxury Add Customer Card
                    MDCard:
                        elevation: 12
                        shadow_radius: 20
                        shadow_color: [0.8, 0.2, 0.4, 0.3]
                        radius: [18, 18, 18, 18]
                        md_bg_color: "#FFFFFF"
                        on_release: app.navigate_to("customers")

                        MDBoxLayout:
                            orientation: "vertical"
                            padding: "20dp"
                            spacing: "12dp"

                            MDIcon:
                                icon: "account-star-outline"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Custom"
                                icon_color: "#C2185B"
                                halign: "center"

                            MDLabel:
                                text: "VIP Clients"
                                role: "medium"
                                theme_text_color: "Custom"
                                text_color: "#C2185B"
                                halign: "center"
                                font_style: "Title"

                    # Luxury New Sale Card
                    MDCard:
                        elevation: 12
                        shadow_radius: 20
                        shadow_color: [0.2, 0.8, 0.4, 0.3]
                        radius: [18, 18, 18, 18]
                        md_bg_color: "#FFFFFF"
                        on_release: app.navigate_to("sales")

                        MDBoxLayout:
                            orientation: "vertical"
                            padding: "20dp"
                            spacing: "12dp"

                            MDIcon:
                                icon: "diamond-stone"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Custom"
                                icon_color: "#388E3C"
                                halign: "center"

                            MDLabel:
                                text: "Elite Sales"
                                role: "medium"
                                theme_text_color: "Custom"
                                text_color: "#388E3C"
                                halign: "center"
                                font_style: "Title"

        # Cars Management Screen
        MDScreen:
            name: "cars"

            MDTopAppBar:
                pos_hint: {"top": 1}
                elevation: 4
                title: "Cars Management"
                left_action_items: [["arrow-left", lambda x: app.navigate_to("dashboard")]]
                right_action_items: [["plus", lambda x: app.show_add_car_dialog()]]

            MDBoxLayout:
                orientation: "vertical"
                padding: "20dp"
                spacing: "15dp"
                pos_hint: {"top": 0.9}

                # Search and Filter
                MDBoxLayout:
                    orientation: "horizontal"
                    spacing: "10dp"
                    size_hint_y: None
                    height: "56dp"

                    MDTextField:
                        id: car_search_field
                        hint_text: "Search cars..."
                        icon_left: "magnify"
                        mode: "outlined"
                        on_text: app.search_cars(self.text)

                    MDIconButton:
                        icon: "filter"
                        on_release: app.show_car_filter_dialog()

                # Cars List
                ScrollView:
                    MDList:
                        id: cars_list

        # Customers Management Screen
        MDScreen:
            name: "customers"

            MDTopAppBar:
                pos_hint: {"top": 1}
                elevation: 4
                title: "Customers Management"
                left_action_items: [["arrow-left", lambda x: app.navigate_to("dashboard")]]
                right_action_items: [["plus", lambda x: app.show_add_customer_dialog()]]

            MDBoxLayout:
                orientation: "vertical"
                padding: "20dp"
                spacing: "15dp"
                pos_hint: {"top": 0.9}

                # Search
                MDTextField:
                    id: customer_search_field
                    hint_text: "Search customers..."
                    icon_left: "magnify"
                    mode: "outlined"
                    size_hint_y: None
                    height: "56dp"
                    on_text: app.search_customers(self.text)

                # Customers List
                ScrollView:
                    MDList:
                        id: customers_list

        # Sales Management Screen
        MDScreen:
            name: "sales"

            MDTopAppBar:
                pos_hint: {"top": 1}
                elevation: 4
                title: "Sales Management"
                left_action_items: [["arrow-left", lambda x: app.navigate_to("dashboard")]]
                right_action_items: [["plus", lambda x: app.show_add_sale_dialog()]]

            MDBoxLayout:
                orientation: "vertical"
                padding: "20dp"
                spacing: "15dp"
                pos_hint: {"top": 0.9}

                # Date Filter
                MDBoxLayout:
                    orientation: "horizontal"
                    spacing: "10dp"
                    size_hint_y: None
                    height: "56dp"

                    MDTextField:
                        id: sales_search_field
                        hint_text: "Search sales..."
                        icon_left: "magnify"
                        mode: "outlined"
                        on_text: app.search_sales(self.text)

                    MDIconButton:
                        icon: "calendar"
                        on_release: app.show_date_filter_dialog()

                # Sales List
                ScrollView:
                    MDList:
                        id: sales_list

        # Maintenance Management Screen
        MDScreen:
            name: "maintenance"

            MDTopAppBar:
                pos_hint: {"top": 1}
                elevation: 4
                title: "Maintenance Management"
                left_action_items: [["arrow-left", lambda x: app.navigate_to("dashboard")]]
                right_action_items: [["plus", lambda x: app.show_add_maintenance_dialog()]]

            MDBoxLayout:
                orientation: "vertical"
                padding: "20dp"
                spacing: "15dp"
                pos_hint: {"top": 0.9}

                # Filter Options
                MDBoxLayout:
                    orientation: "horizontal"
                    spacing: "10dp"
                    size_hint_y: None
                    height: "56dp"

                    MDTextField:
                        id: maintenance_search_field
                        hint_text: "Search maintenance records..."
                        icon_left: "magnify"
                        mode: "outlined"
                        on_text: app.search_maintenance(self.text)

                    MDSegmentedButton:
                        id: maintenance_status_filter

                        MDSegmentedButtonItem:
                            text: "All"
                            active: True

                        MDSegmentedButtonItem:
                            text: "Pending"

                        MDSegmentedButtonItem:
                            text: "Completed"

                # Maintenance List
                ScrollView:
                    MDList:
                        id: maintenance_list

        # Reports Screen
        MDScreen:
            name: "reports"

            MDTopAppBar:
                pos_hint: {"top": 1}
                elevation: 4
                title: "Reports"
                left_action_items: [["arrow-left", lambda x: app.navigate_to("dashboard")]]

            MDBoxLayout:
                orientation: "vertical"
                padding: "20dp"
                spacing: "20dp"
                pos_hint: {"top": 0.9}

                MDLabel:
                    text: "Generate Reports"
                    role: "large"
                    size_hint_y: None
                    height: self.texture_size[1]

                MDGridLayout:
                    cols: 2
                    spacing: "15dp"
                    size_hint_y: None
                    height: "300dp"

                    MDCard:
                        elevation: 3
                        on_release: app.generate_sales_report()

                        MDBoxLayout:
                            orientation: "vertical"
                            padding: "20dp"
                            spacing: "15dp"

                            MDIcon:
                                icon: "chart-line"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Primary"

                            MDLabel:
                                text: "Sales Report"
                                role: "medium"
                                halign: "center"

                            MDLabel:
                                text: "Generate detailed sales analytics"
                                role: "small"
                                halign: "center"
                                theme_text_color: "Secondary"

                    MDCard:
                        elevation: 3
                        on_release: app.generate_inventory_report()

                        MDBoxLayout:
                            orientation: "vertical"
                            padding: "20dp"
                            spacing: "15dp"

                            MDIcon:
                                icon: "car-multiple"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Primary"

                            MDLabel:
                                text: "Inventory Report"
                                role: "medium"
                                halign: "center"

                            MDLabel:
                                text: "Current cars inventory status"
                                role: "small"
                                halign: "center"
                                theme_text_color: "Secondary"

                    MDCard:
                        elevation: 3
                        on_release: app.generate_customer_report()

                        MDBoxLayout:
                            orientation: "vertical"
                            padding: "20dp"
                            spacing: "15dp"

                            MDIcon:
                                icon: "account-group"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Primary"

                            MDLabel:
                                text: "Customer Report"
                                role: "medium"
                                halign: "center"

                            MDLabel:
                                text: "Customer analytics and insights"
                                role: "small"
                                halign: "center"
                                theme_text_color: "Secondary"

                    MDCard:
                        elevation: 3
                        on_release: app.generate_maintenance_report()

                        MDBoxLayout:
                            orientation: "vertical"
                            padding: "20dp"
                            spacing: "15dp"

                            MDIcon:
                                icon: "cog"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Primary"

                            MDLabel:
                                text: "Maintenance Report"
                                role: "medium"
                                halign: "center"

                            MDLabel:
                                text: "Maintenance schedules and history"
                                role: "small"
                                halign: "center"
                                theme_text_color: "Secondary"

    MDNavigationDrawer:
        id: nav_drawer
        radius: (0, 25, 25, 0)
        type: "standard"
        elevation: 20
        shadow_radius: 30
        md_bg_color: "#FFFFFF"

        ContentNavigationDrawer:
            id: content_drawer

<ContentNavigationDrawer>
    orientation: "vertical"
    padding: "20dp"
    spacing: "15dp"
    md_bg_color: "#FFFFFF"

    # Luxury Header Section
    MDCard:
        size_hint_y: None
        height: "160dp"
        elevation: 8
        radius: [20, 20, 20, 20]
        md_bg_color: "#E3F2FD"
        padding: "20dp"

        canvas.before:
            Color:
                rgba: 0.1, 0.3, 0.8, 0.1
            RoundedRectangle:
                pos: self.pos
                size: self.size
                radius: [20, 20, 20, 20]

        MDBoxLayout:
            orientation: "vertical"
            spacing: "12dp"

            MDIcon:
                icon: "car-luxury"
                size_hint_y: None
                height: "56dp"
                theme_icon_color: "Custom"
                icon_color: "#1976D2"
                halign: "center"

            MDLabel:
                text: "Elite Showroom"
                role: "medium"
                theme_text_color: "Custom"
                text_color: "#1976D2"
                halign: "center"
                font_style: "Title"
                size_hint_y: None
                height: self.texture_size[1]

            MDLabel:
                text: "Luxury Management"
                role: "small"
                halign: "center"
                theme_text_color: "Custom"
                text_color: "#1565C0"
                font_style: "Body"
                size_hint_y: None
                height: self.texture_size[1]

    # Elegant Divider
    MDDivider:
        height: "2dp"
        md_bg_color: "#E0E0E0"

    ScrollView:
        MDList:
            id: md_list
            spacing: "8dp"
