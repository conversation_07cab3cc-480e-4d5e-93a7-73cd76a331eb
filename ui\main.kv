
MDNavigationLayout:
    MDScreenManager:
        id: screen_manager

        # Login Screen
        MDScreen:
            name: "login"

            MDCard:
                size_hint: None, None
                size: "400dp", "500dp"
                pos_hint: {"center_x": 0.5, "center_y": 0.5}
                elevation: 10
                padding: "25dp"
                spacing: "20dp"
                orientation: "vertical"
                md_bg_color: app.theme_cls.surfaceColor

                MDLabel:
                    text: "🚗 Car Showroom"
                    role: "headline_large"
                    halign: "center"
                    theme_text_color: "Primary"

                MDLabel:
                    text: "Management System"
                    role: "title_medium"
                    halign: "center"
                    theme_text_color: "Secondary"

                Widget:
                    size_hint_y: None
                    height: "20dp"

                MDTextField:
                    id: username_field
                    hint_text: "Username"
                    icon_left: "account"
                    mode: "outlined"

                MDTextField:
                    id: password_field
                    hint_text: "Password"
                    icon_left: "lock"
                    password: True
                    mode: "outlined"

                Widget:
                    size_hint_y: None
                    height: "10dp"

                MDButton:
                    style: "filled"
                    theme_bg_color: "Primary"
                    on_release: app.login()

                    MDButtonText:
                        text: "Login"

                MDButton:
                    style: "text"
                    on_release: app.show_register_dialog()

                    MDButtonText:
                        text: "Create New Account"

        # Main Dashboard Screen
        MDScreen:
            name: "dashboard"

            MDTopAppBar:
                id: top_app_bar
                pos_hint: {"top": 1}
                elevation: 4
                title: "Car Showroom Dashboard"
                left_action_items: [["menu", lambda x: nav_drawer.set_state("open")]]
                right_action_items: [["logout", lambda x: app.logout()]]

            MDBoxLayout:
                orientation: "vertical"
                padding: "20dp"
                spacing: "20dp"
                pos_hint: {"top": 0.9}

                # Statistics Cards
                MDGridLayout:
                    cols: 2
                    spacing: "15dp"
                    size_hint_y: None
                    height: "200dp"

                    MDCard:
                        elevation: 5
                        padding: "20dp"
                        md_bg_color: app.theme_cls.primaryColor

                        MDBoxLayout:
                            orientation: "vertical"

                            MDIcon:
                                icon: "car"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Custom"
                                icon_color: "white"

                            MDLabel:
                                text: "Total Cars"
                                role: "title_medium"
                                theme_text_color: "Custom"
                                text_color: "white"

                            MDLabel:
                                id: total_cars_label
                                text: "0"
                                role: "headline_large"
                                theme_text_color: "Custom"
                                text_color: "white"

                    MDCard:
                        elevation: 5
                        padding: "20dp"
                        md_bg_color: app.theme_cls.secondaryColor

                        MDBoxLayout:
                            orientation: "vertical"

                            MDIcon:
                                icon: "account-group"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Custom"
                                icon_color: "white"

                            MDLabel:
                                text: "Total Customers"
                                role: "title_medium"
                                theme_text_color: "Custom"
                                text_color: "white"

                            MDLabel:
                                id: total_customers_label
                                text: "0"
                                role: "headline_large"
                                theme_text_color: "Custom"
                                text_color: "white"

                    MDCard:
                        elevation: 5
                        padding: "20dp"
                        md_bg_color: app.theme_cls.tertiaryColor

                        MDBoxLayout:
                            orientation: "vertical"

                            MDIcon:
                                icon: "cart"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Custom"
                                icon_color: "white"

                            MDLabel:
                                text: "Total Sales"
                                role: "title_medium"
                                theme_text_color: "Custom"
                                text_color: "white"

                            MDLabel:
                                id: total_sales_label
                                text: "0"
                                role: "headline_large"
                                theme_text_color: "Custom"
                                text_color: "white"

                    MDCard:
                        elevation: 5
                        padding: "20dp"
                        md_bg_color: app.theme_cls.errorColor

                        MDBoxLayout:
                            orientation: "vertical"

                            MDIcon:
                                icon: "currency-usd"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Custom"
                                icon_color: "white"

                            MDLabel:
                                text: "Revenue"
                                role: "title_medium"
                                theme_text_color: "Custom"
                                text_color: "white"

                            MDLabel:
                                id: total_revenue_label
                                text: "$0"
                                role: "headline_large"
                                theme_text_color: "Custom"
                                text_color: "white"

                # Quick Actions
                MDLabel:
                    text: "Quick Actions"
                    role: "title_large"
                    size_hint_y: None
                    height: self.texture_size[1]

                MDGridLayout:
                    cols: 3
                    spacing: "15dp"
                    size_hint_y: None
                    height: "120dp"

                    MDCard:
                        elevation: 3
                        on_release: app.navigate_to("cars")

                        MDBoxLayout:
                            orientation: "vertical"
                            padding: "15dp"
                            spacing: "10dp"

                            MDIcon:
                                icon: "car-plus"
                                size_hint_y: None
                                height: "40dp"
                                theme_icon_color: "Primary"

                            MDLabel:
                                text: "Add Car"
                                role: "body_medium"
                                halign: "center"

                    MDCard:
                        elevation: 3
                        on_release: app.navigate_to("customers")

                        MDBoxLayout:
                            orientation: "vertical"
                            padding: "15dp"
                            spacing: "10dp"

                            MDIcon:
                                icon: "account-plus"
                                size_hint_y: None
                                height: "40dp"
                                theme_icon_color: "Primary"

                            MDLabel:
                                text: "Add Customer"
                                role: "body_medium"
                                halign: "center"

                    MDCard:
                        elevation: 3
                        on_release: app.navigate_to("sales")

                        MDBoxLayout:
                            orientation: "vertical"
                            padding: "15dp"
                            spacing: "10dp"

                            MDIcon:
                                icon: "cart-plus"
                                size_hint_y: None
                                height: "40dp"
                                theme_icon_color: "Primary"

                            MDLabel:
                                text: "New Sale"
                                role: "body_medium"
                                halign: "center"

        # Cars Management Screen
        MDScreen:
            name: "cars"

            MDTopAppBar:
                pos_hint: {"top": 1}
                elevation: 4
                title: "Cars Management"
                left_action_items: [["arrow-left", lambda x: app.navigate_to("dashboard")]]
                right_action_items: [["plus", lambda x: app.show_add_car_dialog()]]

            MDBoxLayout:
                orientation: "vertical"
                padding: "20dp"
                spacing: "15dp"
                pos_hint: {"top": 0.9}

                # Search and Filter
                MDBoxLayout:
                    orientation: "horizontal"
                    spacing: "10dp"
                    size_hint_y: None
                    height: "56dp"

                    MDTextField:
                        id: car_search_field
                        hint_text: "Search cars..."
                        icon_left: "magnify"
                        mode: "outlined"
                        on_text: app.search_cars(self.text)

                    MDIconButton:
                        icon: "filter"
                        on_release: app.show_car_filter_dialog()

                # Cars List
                ScrollView:
                    MDList:
                        id: cars_list

        # Customers Management Screen
        MDScreen:
            name: "customers"

            MDTopAppBar:
                pos_hint: {"top": 1}
                elevation: 4
                title: "Customers Management"
                left_action_items: [["arrow-left", lambda x: app.navigate_to("dashboard")]]
                right_action_items: [["plus", lambda x: app.show_add_customer_dialog()]]

            MDBoxLayout:
                orientation: "vertical"
                padding: "20dp"
                spacing: "15dp"
                pos_hint: {"top": 0.9}

                # Search
                MDTextField:
                    id: customer_search_field
                    hint_text: "Search customers..."
                    icon_left: "magnify"
                    mode: "outlined"
                    size_hint_y: None
                    height: "56dp"
                    on_text: app.search_customers(self.text)

                # Customers List
                ScrollView:
                    MDList:
                        id: customers_list

        # Sales Management Screen
        MDScreen:
            name: "sales"

            MDTopAppBar:
                pos_hint: {"top": 1}
                elevation: 4
                title: "Sales Management"
                left_action_items: [["arrow-left", lambda x: app.navigate_to("dashboard")]]
                right_action_items: [["plus", lambda x: app.show_add_sale_dialog()]]

            MDBoxLayout:
                orientation: "vertical"
                padding: "20dp"
                spacing: "15dp"
                pos_hint: {"top": 0.9}

                # Date Filter
                MDBoxLayout:
                    orientation: "horizontal"
                    spacing: "10dp"
                    size_hint_y: None
                    height: "56dp"

                    MDTextField:
                        id: sales_search_field
                        hint_text: "Search sales..."
                        icon_left: "magnify"
                        mode: "outlined"
                        on_text: app.search_sales(self.text)

                    MDIconButton:
                        icon: "calendar"
                        on_release: app.show_date_filter_dialog()

                # Sales List
                ScrollView:
                    MDList:
                        id: sales_list

        # Maintenance Management Screen
        MDScreen:
            name: "maintenance"

            MDTopAppBar:
                pos_hint: {"top": 1}
                elevation: 4
                title: "Maintenance Management"
                left_action_items: [["arrow-left", lambda x: app.navigate_to("dashboard")]]
                right_action_items: [["plus", lambda x: app.show_add_maintenance_dialog()]]

            MDBoxLayout:
                orientation: "vertical"
                padding: "20dp"
                spacing: "15dp"
                pos_hint: {"top": 0.9}

                # Filter Options
                MDBoxLayout:
                    orientation: "horizontal"
                    spacing: "10dp"
                    size_hint_y: None
                    height: "56dp"

                    MDTextField:
                        id: maintenance_search_field
                        hint_text: "Search maintenance records..."
                        icon_left: "magnify"
                        mode: "outlined"
                        on_text: app.search_maintenance(self.text)

                    MDSegmentedButton:
                        id: maintenance_status_filter

                        MDSegmentedButtonItem:
                            text: "All"
                            active: True

                        MDSegmentedButtonItem:
                            text: "Pending"

                        MDSegmentedButtonItem:
                            text: "Completed"

                # Maintenance List
                ScrollView:
                    MDList:
                        id: maintenance_list

        # Reports Screen
        MDScreen:
            name: "reports"

            MDTopAppBar:
                pos_hint: {"top": 1}
                elevation: 4
                title: "Reports"
                left_action_items: [["arrow-left", lambda x: app.navigate_to("dashboard")]]

            MDBoxLayout:
                orientation: "vertical"
                padding: "20dp"
                spacing: "20dp"
                pos_hint: {"top": 0.9}

                MDLabel:
                    text: "Generate Reports"
                    role: "title_large"
                    size_hint_y: None
                    height: self.texture_size[1]

                MDGridLayout:
                    cols: 2
                    spacing: "15dp"
                    size_hint_y: None
                    height: "300dp"

                    MDCard:
                        elevation: 3
                        on_release: app.generate_sales_report()

                        MDBoxLayout:
                            orientation: "vertical"
                            padding: "20dp"
                            spacing: "15dp"

                            MDIcon:
                                icon: "chart-line"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Primary"

                            MDLabel:
                                text: "Sales Report"
                                role: "title_medium"
                                halign: "center"

                            MDLabel:
                                text: "Generate detailed sales analytics"
                                role: "body_small"
                                halign: "center"
                                theme_text_color: "Secondary"

                    MDCard:
                        elevation: 3
                        on_release: app.generate_inventory_report()

                        MDBoxLayout:
                            orientation: "vertical"
                            padding: "20dp"
                            spacing: "15dp"

                            MDIcon:
                                icon: "car-multiple"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Primary"

                            MDLabel:
                                text: "Inventory Report"
                                role: "title_medium"
                                halign: "center"

                            MDLabel:
                                text: "Current cars inventory status"
                                role: "body_small"
                                halign: "center"
                                theme_text_color: "Secondary"

                    MDCard:
                        elevation: 3
                        on_release: app.generate_customer_report()

                        MDBoxLayout:
                            orientation: "vertical"
                            padding: "20dp"
                            spacing: "15dp"

                            MDIcon:
                                icon: "account-group"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Primary"

                            MDLabel:
                                text: "Customer Report"
                                role: "title_medium"
                                halign: "center"

                            MDLabel:
                                text: "Customer analytics and insights"
                                role: "body_small"
                                halign: "center"
                                theme_text_color: "Secondary"

                    MDCard:
                        elevation: 3
                        on_release: app.generate_maintenance_report()

                        MDBoxLayout:
                            orientation: "vertical"
                            padding: "20dp"
                            spacing: "15dp"

                            MDIcon:
                                icon: "cog"
                                size_hint_y: None
                                height: "48dp"
                                theme_icon_color: "Primary"

                            MDLabel:
                                text: "Maintenance Report"
                                role: "title_medium"
                                halign: "center"

                            MDLabel:
                                text: "Maintenance schedules and history"
                                role: "body_small"
                                halign: "center"
                                theme_text_color: "Secondary"

    MDNavigationDrawer:
        id: nav_drawer
        radius: (0, 16, 16, 0)
        type: "standard"

        ContentNavigationDrawer:
            id: content_drawer

<ContentNavigationDrawer>
    orientation: "vertical"
    padding: "8dp"
    spacing: "8dp"

    MDBoxLayout:
        orientation: "vertical"
        spacing: "10dp"
        size_hint_y: None
        height: "120dp"

        MDIcon:
            icon: "car"
            size_hint_y: None
            height: "48dp"
            theme_icon_color: "Primary"

        MDLabel:
            text: "Car Showroom"
            role: "headline_small"
            halign: "center"
            size_hint_y: None
            height: self.texture_size[1]

        MDLabel:
            text: "Management System"
            role: "body_small"
            halign: "center"
            theme_text_color: "Secondary"
            size_hint_y: None
            height: self.texture_size[1]

    MDDivider:

    ScrollView:
        MDList:
            id: md_list
