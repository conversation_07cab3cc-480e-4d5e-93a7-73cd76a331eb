"""
Customer management service
"""

from sqlalchemy.orm import Session
from sqlalchemy import or_
from models.database import get_session
from models.customer import Customer
from typing import Optional, Dict, Any, List
from datetime import datetime, date

class CustomerService:

    def add_customer(self, customer_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add a new customer"""
        db = get_session()
        try:
            # Check if email already exists
            if customer_data.get('email'):
                existing_customer = db.query(Customer).filter(
                    Customer.email == customer_data.get('email')
                ).first()
                if existing_customer:
                    return {
                        'success': False,
                        'message': 'Customer with this email already exists'
                    }

            # Parse date of birth if provided
            date_of_birth = None
            if customer_data.get('date_of_birth'):
                if isinstance(customer_data['date_of_birth'], str):
                    date_of_birth = datetime.strptime(customer_data['date_of_birth'], '%Y-%m-%d').date()
                else:
                    date_of_birth = customer_data['date_of_birth']

            # Create new customer
            new_customer = Customer(
                first_name=customer_data.get('first_name'),
                last_name=customer_data.get('last_name'),
                email=customer_data.get('email'),
                phone=customer_data.get('phone'),
                address=customer_data.get('address'),
                city=customer_data.get('city'),
                state=customer_data.get('state'),
                zip_code=customer_data.get('zip_code'),
                date_of_birth=date_of_birth,
                driver_license=customer_data.get('driver_license'),
                preferred_contact=customer_data.get('preferred_contact', 'phone'),
                notes=customer_data.get('notes')
            )

            db.add(new_customer)
            db.commit()
            db.refresh(new_customer)

            return {
                'success': True,
                'message': 'Customer added successfully',
                'customer': new_customer.to_dict()
            }

        except Exception as e:
            db.rollback()
            return {
                'success': False,
                'message': f'Error adding customer: {str(e)}'
            }
        finally:
            db.close()

    def get_customer_by_id(self, customer_id: int) -> Optional[Customer]:
        """Get customer by ID"""
        db = get_session()
        try:
            return db.query(Customer).filter(Customer.id == customer_id).first()
        finally:
            db.close()

    def get_customer_by_email(self, email: str) -> Optional[Customer]:
        """Get customer by email"""
        db = get_session()
        try:
            return db.query(Customer).filter(Customer.email == email).first()
        finally:
            db.close()

    def get_all_customers(self, active_only: bool = True) -> List[Customer]:
        """Get all customers"""
        db = get_session()
        try:
            query = db.query(Customer)
            if active_only:
                query = query.filter(Customer.is_active == True)
            return query.all()
        finally:
            db.close()

    def search_customers(self, search_term: str) -> List[Customer]:
        """Search customers by name, email, or phone"""
        db = get_session()
        try:
            search_pattern = f"%{search_term}%"
            return db.query(Customer).filter(
                or_(
                    Customer.first_name.ilike(search_pattern),
                    Customer.last_name.ilike(search_pattern),
                    Customer.email.ilike(search_pattern),
                    Customer.phone.ilike(search_pattern)
                )
            ).all()
        finally:
            db.close()

    def update_customer(self, customer_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update customer information"""
        db = get_session()
        try:
            customer = db.query(Customer).filter(Customer.id == customer_id).first()
            if not customer:
                return {
                    'success': False,
                    'message': 'Customer not found'
                }

            # Check if email is being updated and already exists
            if 'email' in update_data and update_data['email'] != customer.email:
                existing_customer = db.query(Customer).filter(
                    Customer.email == update_data['email'],
                    Customer.id != customer_id
                ).first()
                if existing_customer:
                    return {
                        'success': False,
                        'message': 'Email already exists for another customer'
                    }

            # Parse date of birth if provided
            if 'date_of_birth' in update_data and update_data['date_of_birth']:
                if isinstance(update_data['date_of_birth'], str):
                    update_data['date_of_birth'] = datetime.strptime(
                        update_data['date_of_birth'], '%Y-%m-%d'
                    ).date()

            # Update fields
            for field, value in update_data.items():
                if hasattr(customer, field):
                    setattr(customer, field, value)

            db.commit()
            db.refresh(customer)

            return {
                'success': True,
                'message': 'Customer updated successfully',
                'customer': customer.to_dict()
            }

        except Exception as e:
            db.rollback()
            return {
                'success': False,
                'message': f'Error updating customer: {str(e)}'
            }
        finally:
            db.close()

    def deactivate_customer(self, customer_id: int) -> Dict[str, Any]:
        """Deactivate a customer (soft delete)"""
        return self.update_customer(customer_id, {'is_active': False})

    def activate_customer(self, customer_id: int) -> Dict[str, Any]:
        """Activate a customer"""
        return self.update_customer(customer_id, {'is_active': True})

    def delete_customer(self, customer_id: int) -> Dict[str, Any]:
        """Delete a customer (hard delete)"""
        db = get_session()
        try:
            customer = db.query(Customer).filter(Customer.id == customer_id).first()
            if not customer:
                return {
                    'success': False,
                    'message': 'Customer not found'
                }

            # Check if customer has purchase history
            if hasattr(customer, 'purchases') and customer.purchases:
                return {
                    'success': False,
                    'message': 'Cannot delete customer with purchase history. Use deactivate instead.'
                }

            db.delete(customer)
            db.commit()

            return {
                'success': True,
                'message': 'Customer deleted successfully'
            }

        except Exception as e:
            db.rollback()
            return {
                'success': False,
                'message': f'Error deleting customer: {str(e)}'
            }
        finally:
            db.close()

    def get_customer_stats(self) -> Dict[str, Any]:
        """Get customer statistics"""
        db = get_session()
        try:
            total_customers = db.query(Customer).count()
            active_customers = db.query(Customer).filter(Customer.is_active == True).count()
            inactive_customers = total_customers - active_customers

            # Get customers by city
            from sqlalchemy import func
            city_stats = db.query(Customer.city, func.count(Customer.id)).filter(
                Customer.city.isnot(None),
                Customer.is_active == True
            ).group_by(Customer.city).all()

            return {
                'total_customers': total_customers,
                'active_customers': active_customers,
                'inactive_customers': inactive_customers,
                'customers_by_city': {city: count for city, count in city_stats}
            }
        finally:
            db.close()

    def get_customers_with_purchases(self) -> List[Customer]:
        """Get customers who have made purchases"""
        db = get_session()
        try:
            return db.query(Customer).join(Customer.purchases).distinct().all()
        finally:
            db.close()

    def get_customer_purchase_history(self, customer_id: int) -> Dict[str, Any]:
        """Get customer's purchase history"""
        db = get_session()
        try:
            customer = db.query(Customer).filter(Customer.id == customer_id).first()
            if not customer:
                return {
                    'success': False,
                    'message': 'Customer not found'
                }

            purchases = []
            total_spent = 0

            if hasattr(customer, 'purchases'):
                for sale in customer.purchases:
                    purchases.append(sale.to_dict())
                    total_spent += sale.total_amount

            return {
                'success': True,
                'customer': customer.to_dict(),
                'purchases': purchases,
                'total_spent': total_spent,
                'purchase_count': len(purchases)
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'Error retrieving purchase history: {str(e)}'
            }
        finally:
            db.close()

# Global customer service instance
customer_service = CustomerService()
