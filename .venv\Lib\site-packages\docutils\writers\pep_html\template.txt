<?xml version="1.0" encoding="%(encoding)s"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<!--
This HTML is auto-generated.  DO NOT EDIT THIS FILE!  If you are writing a new
PEP, see http://peps.python.org/pep-0001 for instructions and links
to templates.  DO NOT USE THIS HTML FILE AS YOUR TEMPLATE!
-->
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=%(encoding)s" />
  <meta name="generator" content="Docutils %(version)s: https://docutils.sourceforge.io/" />
  <title>PEP %(pep)s - %(title)s</title>
  %(stylesheet)s
</head>
<body bgcolor="white">
<div class="header">
<strong>Python Enhancement Proposals</strong>
| <a href="%(pyhome)s/">Python</a>
&raquo; <a href="https://peps.python.org/pep-0000/">PEP Index</a>
&raquo; PEP %(pep)s &ndash; %(title)s
<hr class="header"/>
</div>
<div class="document">
%(body)s
%(body_suffix)s
