[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "car-showroom-system"
version = "1.0.0"
description = "نظام إدارة معرض السيارات المتكامل"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Car Showroom Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Car Showroom Team", email = "<EMAIL>"}
]
keywords = ["car", "showroom", "management", "inventory", "sales", "customers"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Office/Business",
    "Topic :: Database :: Front-Ends",
]
requires-python = ">=3.8"
dependencies = [
    "kivy>=2.1.0",
    "kivymd>=1.1.1",
    "sqlalchemy>=1.4.0",
    "bcrypt>=4.0.0",
    "reportlab>=3.6.0",
    "Pillow>=9.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "flake8>=4.0.0",
    "mypy>=0.950",
]
build = [
    "pyinstaller>=5.0.0",
]

[project.urls]
Homepage = "https://github.com/your-repo/car-showroom-system"
Documentation = "https://github.com/your-repo/car-showroom-system/wiki"
Repository = "https://github.com/your-repo/car-showroom-system.git"
"Bug Tracker" = "https://github.com/your-repo/car-showroom-system/issues"

[project.scripts]
car-showroom = "app.main:main"

[tool.setuptools]
packages = ["app", "core", "models", "services", "ui"]

[tool.setuptools.package-data]
"*" = ["*.kv", "*.png", "*.jpg", "*.ico"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    ".venv",
    "build",
    "dist",
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
