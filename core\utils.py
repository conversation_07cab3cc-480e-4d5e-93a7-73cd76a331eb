"""
Utility functions for Car Showroom System
"""

import os
import json
import uuid
import hashlib
from datetime import datetime, date
from typing import Any, Dict, List, Optional
import shutil

def generate_unique_id() -> str:
    """Generate a unique ID"""
    return str(uuid.uuid4())

def generate_work_order_number() -> str:
    """Generate a unique work order number"""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    return f"WO{timestamp}"

def generate_sale_number() -> str:
    """Generate a unique sale number"""
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    return f"SALE{timestamp}"

def format_currency(amount: float, currency_symbol: str = "$") -> str:
    """Format amount as currency"""
    return f"{currency_symbol}{amount:,.2f}"

def format_date(date_obj: Optional[date], format_str: str = "%Y-%m-%d") -> str:
    """Format date object to string"""
    if date_obj is None:
        return ""
    return date_obj.strftime(format_str)

def format_datetime(datetime_obj: Optional[datetime], format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """Format datetime object to string"""
    if datetime_obj is None:
        return ""
    return datetime_obj.strftime(format_str)

def parse_date(date_str: str, format_str: str = "%Y-%m-%d") -> Optional[date]:
    """Parse date string to date object"""
    try:
        return datetime.strptime(date_str, format_str).date()
    except (ValueError, TypeError):
        return None

def parse_datetime(datetime_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> Optional[datetime]:
    """Parse datetime string to datetime object"""
    try:
        return datetime.strptime(datetime_str, format_str)
    except (ValueError, TypeError):
        return None

def validate_email(email: str) -> bool:
    """Validate email format"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone: str) -> bool:
    """Validate phone number format"""
    import re
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    # Check if it has 10-15 digits
    return 10 <= len(digits_only) <= 15

def validate_vin(vin: str) -> bool:
    """Validate VIN (Vehicle Identification Number)"""
    if len(vin) != 17:
        return False
    
    # VIN should not contain I, O, or Q
    forbidden_chars = {'I', 'O', 'Q'}
    return not any(char in forbidden_chars for char in vin.upper())

def calculate_age_from_birth_date(birth_date: date) -> int:
    """Calculate age from birth date"""
    today = date.today()
    return today.year - birth_date.year - ((today.month, today.day) < (birth_date.month, birth_date.day))

def calculate_loan_payment(principal: float, annual_rate: float, months: int) -> float:
    """Calculate monthly loan payment"""
    if annual_rate == 0:
        return principal / months
    
    monthly_rate = annual_rate / 12 / 100
    payment = principal * (monthly_rate * (1 + monthly_rate) ** months) / ((1 + monthly_rate) ** months - 1)
    return round(payment, 2)

def save_json_file(data: Dict[str, Any], file_path: str) -> bool:
    """Save data to JSON file"""
    try:
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)
        return True
    except Exception as e:
        print(f"Error saving JSON file: {e}")
        return False

def load_json_file(file_path: str) -> Optional[Dict[str, Any]]:
    """Load data from JSON file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        return None

def backup_file(source_path: str, backup_dir: str) -> bool:
    """Create backup of a file"""
    try:
        if not os.path.exists(source_path):
            return False
        
        os.makedirs(backup_dir, exist_ok=True)
        filename = os.path.basename(source_path)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"{timestamp}_{filename}"
        backup_path = os.path.join(backup_dir, backup_filename)
        
        shutil.copy2(source_path, backup_path)
        return True
    except Exception as e:
        print(f"Error creating backup: {e}")
        return False

def get_file_hash(file_path: str) -> Optional[str]:
    """Get MD5 hash of a file"""
    try:
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        print(f"Error calculating file hash: {e}")
        return None

def clean_string(text: str) -> str:
    """Clean and normalize string"""
    if not text:
        return ""
    return text.strip().replace('\n', ' ').replace('\r', ' ')

def truncate_string(text: str, max_length: int, suffix: str = "...") -> str:
    """Truncate string to maximum length"""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix

def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def is_valid_image_file(file_path: str) -> bool:
    """Check if file is a valid image"""
    from core.config import ALLOWED_IMAGE_EXTENSIONS
    
    if not os.path.exists(file_path):
        return False
    
    _, ext = os.path.splitext(file_path.lower())
    return ext in ALLOWED_IMAGE_EXTENSIONS

def create_thumbnail(image_path: str, thumbnail_path: str, size: tuple = (150, 150)) -> bool:
    """Create thumbnail from image"""
    try:
        from PIL import Image
        
        with Image.open(image_path) as img:
            img.thumbnail(size, Image.Resampling.LANCZOS)
            img.save(thumbnail_path, optimize=True, quality=85)
        return True
    except Exception as e:
        print(f"Error creating thumbnail: {e}")
        return False

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage"""
    import re
    
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    
    # Limit length
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename

class DateTimeEncoder(json.JSONEncoder):
    """JSON encoder for datetime objects"""
    
    def default(self, obj):
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super().default(obj)
