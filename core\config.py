"""
Configuration settings for Car Showroom System
"""

import os
from datetime import datetime

# Application Information
APP_NAME = "Car Showroom Management System"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Car Showroom Team"

# Database Configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///car_showroom.db")
DATABASE_ECHO = os.getenv("DATABASE_ECHO", "False").lower() == "true"

# Security Configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
PASSWORD_MIN_LENGTH = 6
SESSION_TIMEOUT_MINUTES = 60

# File Upload Configuration
UPLOAD_FOLDER = "assets/uploads"
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB
ALLOWED_IMAGE_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.gif', '.bmp'}

# Report Configuration
REPORTS_FOLDER = "reports/generated"
REPORT_LOGO_PATH = "assets/images/logo.png"

# UI Configuration
DEFAULT_THEME = "Dark"
PRIMARY_PALETTE = "Amber"
ACCENT_PALETTE = "Orange"

# Business Rules
DEFAULT_TAX_RATE = 0.08  # 8%
DEFAULT_WARRANTY_MONTHS = 12
MAX_DISCOUNT_PERCENTAGE = 50

# Maintenance Configuration
MAINTENANCE_REMINDER_DAYS = 30
DEFAULT_LABOR_RATE = 75.0  # per hour

# Backup Configuration
BACKUP_FOLDER = "backups"
AUTO_BACKUP_ENABLED = True
BACKUP_RETENTION_DAYS = 30

# Logging Configuration
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FILE = "logs/car_showroom.log"

# Create necessary directories
def create_directories():
    """Create necessary directories if they don't exist"""
    directories = [
        UPLOAD_FOLDER,
        REPORTS_FOLDER,
        BACKUP_FOLDER,
        "logs",
        "assets/images",
        "assets/fonts"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

# Initialize configuration
create_directories()
