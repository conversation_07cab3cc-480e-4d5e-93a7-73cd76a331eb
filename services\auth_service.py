"""
Authentication and authorization service
"""

import bcrypt
from sqlalchemy.orm import Session
from models.database import get_session
from models.user import User, UserRole
from typing import Optional, Dict, Any

class AuthService:
    def __init__(self):
        self.current_user: Optional[User] = None
    
    def hash_password(self, password: str) -> str:
        """Hash a password using bcrypt"""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """Verify a password against its hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
    
    def create_user(self, username: str, email: str, full_name: str, 
                   password: str, role: UserRole = UserRole.EMPLOYEE) -> Dict[str, Any]:
        """Create a new user"""
        db = get_session()
        try:
            # Check if username or email already exists
            existing_user = db.query(User).filter(
                (User.username == username) | (User.email == email)
            ).first()
            
            if existing_user:
                return {
                    'success': False,
                    'message': 'Username or email already exists'
                }
            
            # Create new user
            hashed_password = self.hash_password(password)
            new_user = User(
                username=username,
                email=email,
                full_name=full_name,
                password_hash=hashed_password,
                role=role
            )
            
            db.add(new_user)
            db.commit()
            db.refresh(new_user)
            
            return {
                'success': True,
                'message': 'User created successfully',
                'user': new_user.to_dict()
            }
            
        except Exception as e:
            db.rollback()
            return {
                'success': False,
                'message': f'Error creating user: {str(e)}'
            }
        finally:
            db.close()
    
    def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """Authenticate user with username and password"""
        db = get_session()
        try:
            user = db.query(User).filter(
                User.username == username,
                User.is_active == True
            ).first()
            
            if not user:
                return {
                    'success': False,
                    'message': 'Invalid username or password'
                }
            
            if not self.verify_password(password, user.password_hash):
                return {
                    'success': False,
                    'message': 'Invalid username or password'
                }
            
            self.current_user = user
            return {
                'success': True,
                'message': 'Login successful',
                'user': user.to_dict()
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Authentication error: {str(e)}'
            }
        finally:
            db.close()
    
    def logout(self):
        """Logout current user"""
        self.current_user = None
    
    def is_authenticated(self) -> bool:
        """Check if user is authenticated"""
        return self.current_user is not None
    
    def has_permission(self, required_role: UserRole) -> bool:
        """Check if current user has required permission"""
        if not self.is_authenticated():
            return False
        
        # Define role hierarchy
        role_hierarchy = {
            UserRole.ADMIN: 4,
            UserRole.SALES_MANAGER: 3,
            UserRole.MAINTENANCE_TECH: 2,
            UserRole.EMPLOYEE: 1
        }
        
        current_level = role_hierarchy.get(self.current_user.role, 0)
        required_level = role_hierarchy.get(required_role, 0)
        
        return current_level >= required_level
    
    def get_current_user(self) -> Optional[User]:
        """Get current authenticated user"""
        return self.current_user
    
    def change_password(self, old_password: str, new_password: str) -> Dict[str, Any]:
        """Change password for current user"""
        if not self.is_authenticated():
            return {
                'success': False,
                'message': 'User not authenticated'
            }
        
        db = get_session()
        try:
            user = db.query(User).filter(User.id == self.current_user.id).first()
            
            if not self.verify_password(old_password, user.password_hash):
                return {
                    'success': False,
                    'message': 'Current password is incorrect'
                }
            
            # Update password
            user.password_hash = self.hash_password(new_password)
            db.commit()
            
            return {
                'success': True,
                'message': 'Password changed successfully'
            }
            
        except Exception as e:
            db.rollback()
            return {
                'success': False,
                'message': f'Error changing password: {str(e)}'
            }
        finally:
            db.close()
    
    def get_all_users(self) -> list:
        """Get all users (admin only)"""
        if not self.has_permission(UserRole.ADMIN):
            return []
        
        db = get_session()
        try:
            users = db.query(User).all()
            return [user.to_dict() for user in users]
        finally:
            db.close()
    
    def update_user_role(self, user_id: int, new_role: UserRole) -> Dict[str, Any]:
        """Update user role (admin only)"""
        if not self.has_permission(UserRole.ADMIN):
            return {
                'success': False,
                'message': 'Insufficient permissions'
            }
        
        db = get_session()
        try:
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                return {
                    'success': False,
                    'message': 'User not found'
                }
            
            user.role = new_role
            db.commit()
            
            return {
                'success': True,
                'message': 'User role updated successfully'
            }
            
        except Exception as e:
            db.rollback()
            return {
                'success': False,
                'message': f'Error updating user role: {str(e)}'
            }
        finally:
            db.close()

# Global auth service instance
auth_service = AuthService()
