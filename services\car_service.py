"""
Car management service
"""

from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
from models.database import get_session
from models.car import Car, CarStatus, FuelType, Transmission
from typing import Optional, Dict, Any, List
import uuid

class CarService:

    def add_car(self, car_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add a new car to inventory"""
        db = get_session()
        try:
            # Check if VIN already exists
            existing_car = db.query(Car).filter(Car.vin == car_data.get('vin')).first()
            if existing_car:
                return {
                    'success': False,
                    'message': 'Car with this VIN already exists'
                }

            # Create new car
            new_car = Car(
                vin=car_data.get('vin'),
                make=car_data.get('make'),
                model=car_data.get('model'),
                year=car_data.get('year'),
                color=car_data.get('color'),
                mileage=car_data.get('mileage', 0),
                fuel_type=FuelType(car_data.get('fuel_type')),
                transmission=Transmission(car_data.get('transmission')),
                engine_size=car_data.get('engine_size'),
                doors=car_data.get('doors', 4),
                seats=car_data.get('seats', 5),
                purchase_price=car_data.get('purchase_price'),
                selling_price=car_data.get('selling_price'),
                description=car_data.get('description'),
                features=car_data.get('features'),
                image_path=car_data.get('image_path'),
                location=car_data.get('location')
            )

            db.add(new_car)
            db.commit()
            db.refresh(new_car)

            return {
                'success': True,
                'message': 'Car added successfully',
                'car': new_car.to_dict()
            }

        except Exception as e:
            db.rollback()
            return {
                'success': False,
                'message': f'Error adding car: {str(e)}'
            }
        finally:
            db.close()

    def get_car_by_id(self, car_id: int) -> Optional[Car]:
        """Get car by ID"""
        db = get_session()
        try:
            return db.query(Car).filter(Car.id == car_id).first()
        finally:
            db.close()

    def get_car_by_vin(self, vin: str) -> Optional[Car]:
        """Get car by VIN"""
        db = get_session()
        try:
            return db.query(Car).filter(Car.vin == vin).first()
        finally:
            db.close()

    def get_all_cars(self, status_filter: Optional[CarStatus] = None) -> List[Car]:
        """Get all cars with optional status filter"""
        db = get_session()
        try:
            query = db.query(Car)
            if status_filter:
                query = query.filter(Car.status == status_filter)
            return query.all()
        finally:
            db.close()

    def search_cars(self, search_term: str) -> List[Car]:
        """Search cars by make, model, year, or VIN"""
        db = get_session()
        try:
            search_pattern = f"%{search_term}%"
            return db.query(Car).filter(
                or_(
                    Car.make.ilike(search_pattern),
                    Car.model.ilike(search_pattern),
                    Car.vin.ilike(search_pattern),
                    Car.color.ilike(search_pattern)
                )
            ).all()
        finally:
            db.close()

    def filter_cars(self, filters: Dict[str, Any]) -> List[Car]:
        """Filter cars by multiple criteria"""
        db = get_session()
        try:
            query = db.query(Car)

            if filters.get('make'):
                query = query.filter(Car.make.ilike(f"%{filters['make']}%"))

            if filters.get('model'):
                query = query.filter(Car.model.ilike(f"%{filters['model']}%"))

            if filters.get('year_from'):
                query = query.filter(Car.year >= filters['year_from'])

            if filters.get('year_to'):
                query = query.filter(Car.year <= filters['year_to'])

            if filters.get('price_from'):
                query = query.filter(Car.selling_price >= filters['price_from'])

            if filters.get('price_to'):
                query = query.filter(Car.selling_price <= filters['price_to'])

            if filters.get('fuel_type'):
                query = query.filter(Car.fuel_type == FuelType(filters['fuel_type']))

            if filters.get('transmission'):
                query = query.filter(Car.transmission == Transmission(filters['transmission']))

            if filters.get('status'):
                query = query.filter(Car.status == CarStatus(filters['status']))

            return query.all()
        finally:
            db.close()

    def update_car(self, car_id: int, update_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update car information"""
        db = get_session()
        try:
            car = db.query(Car).filter(Car.id == car_id).first()
            if not car:
                return {
                    'success': False,
                    'message': 'Car not found'
                }

            # Update fields
            for field, value in update_data.items():
                if hasattr(car, field) and value is not None:
                    if field == 'fuel_type':
                        setattr(car, field, FuelType(value))
                    elif field == 'transmission':
                        setattr(car, field, Transmission(value))
                    elif field == 'status':
                        setattr(car, field, CarStatus(value))
                    else:
                        setattr(car, field, value)

            db.commit()
            db.refresh(car)

            return {
                'success': True,
                'message': 'Car updated successfully',
                'car': car.to_dict()
            }

        except Exception as e:
            db.rollback()
            return {
                'success': False,
                'message': f'Error updating car: {str(e)}'
            }
        finally:
            db.close()

    def delete_car(self, car_id: int) -> Dict[str, Any]:
        """Delete a car from inventory"""
        db = get_session()
        try:
            car = db.query(Car).filter(Car.id == car_id).first()
            if not car:
                return {
                    'success': False,
                    'message': 'Car not found'
                }

            # Check if car has sales records
            if hasattr(car, 'sales') and car.sales:
                return {
                    'success': False,
                    'message': 'Cannot delete car with existing sales records'
                }

            db.delete(car)
            db.commit()

            return {
                'success': True,
                'message': 'Car deleted successfully'
            }

        except Exception as e:
            db.rollback()
            return {
                'success': False,
                'message': f'Error deleting car: {str(e)}'
            }
        finally:
            db.close()

    def update_car_status(self, car_id: int, new_status: CarStatus) -> Dict[str, Any]:
        """Update car status"""
        return self.update_car(car_id, {'status': new_status.value})

    def get_available_cars(self) -> List[Car]:
        """Get all available cars"""
        return self.get_all_cars(CarStatus.AVAILABLE)

    def get_inventory_stats(self) -> Dict[str, Any]:
        """Get inventory statistics"""
        db = get_session()
        try:
            total_cars = db.query(Car).count()
            available_cars = db.query(Car).filter(Car.status == CarStatus.AVAILABLE).count()
            sold_cars = db.query(Car).filter(Car.status == CarStatus.SOLD).count()
            reserved_cars = db.query(Car).filter(Car.status == CarStatus.RESERVED).count()
            maintenance_cars = db.query(Car).filter(Car.status == CarStatus.MAINTENANCE).count()

            # Calculate total inventory value
            from sqlalchemy import func
            total_value = db.query(func.sum(Car.selling_price)).filter(Car.status == CarStatus.AVAILABLE).scalar() or 0

            return {
                'total_cars': total_cars,
                'available_cars': available_cars,
                'sold_cars': sold_cars,
                'reserved_cars': reserved_cars,
                'maintenance_cars': maintenance_cars,
                'total_inventory_value': total_value
            }
        finally:
            db.close()

    def get_cars_by_make(self) -> Dict[str, int]:
        """Get car count by make"""
        db = get_session()
        try:
            from sqlalchemy import func
            results = db.query(Car.make, func.count(Car.id)).group_by(Car.make).all()
            return {make: count for make, count in results}
        finally:
            db.close()

# Global car service instance
car_service = CarService()
