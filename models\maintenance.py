"""
Maintenance model for service and maintenance tracking
"""

from sqlalchemy import Column, Integer, String, DateTime, Float, Text, ForeignKey, Enum, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .database import Base
import enum

class MaintenanceType(enum.Enum):
    ROUTINE = "routine"
    REPAIR = "repair"
    INSPECTION = "inspection"
    WARRANTY = "warranty"
    RECALL = "recall"

class MaintenanceStatus(enum.Enum):
    SCHEDULED = "scheduled"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    PENDING_PARTS = "pending_parts"

class MaintenancePriority(enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class Maintenance(Base):
    __tablename__ = "maintenance"
    
    id = Column(Integer, primary_key=True, index=True)
    work_order_number = Column(String(20), unique=True, index=True, nullable=False)
    car_id = Column(Integer, ForeignKey("cars.id"), nullable=False)
    customer_id = Column(Integer, ForeignKey("customers.id"))
    technician_id = Column(Integer, ForeignKey("users.id"))
    
    # Maintenance details
    maintenance_type = Column(Enum(MaintenanceType), nullable=False)
    status = Column(Enum(MaintenanceStatus), default=MaintenanceStatus.SCHEDULED, nullable=False)
    priority = Column(Enum(MaintenancePriority), default=MaintenancePriority.MEDIUM, nullable=False)
    
    # Service information
    service_description = Column(Text, nullable=False)
    parts_needed = Column(Text)  # JSON string of parts
    labor_hours = Column(Float, default=0)
    labor_rate = Column(Float, default=0)
    parts_cost = Column(Float, default=0)
    total_cost = Column(Float, default=0)
    
    # Dates
    scheduled_date = Column(DateTime(timezone=True))
    started_date = Column(DateTime(timezone=True))
    completed_date = Column(DateTime(timezone=True))
    next_service_date = Column(DateTime(timezone=True))
    
    # Mileage tracking
    current_mileage = Column(Integer)
    next_service_mileage = Column(Integer)
    
    # Additional information
    notes = Column(Text)
    warranty_covered = Column(Boolean, default=False)
    customer_approved = Column(Boolean, default=False)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    car = relationship("Car", backref="maintenance_records")
    customer = relationship("Customer", backref="maintenance_requests")
    technician = relationship("User", backref="maintenance_work")
    
    def __repr__(self):
        return f"<Maintenance(work_order='{self.work_order_number}', type='{self.maintenance_type.value}')>"
    
    @property
    def total_labor_cost(self):
        return (self.labor_hours or 0) * (self.labor_rate or 0)
    
    @property
    def estimated_total(self):
        return self.total_labor_cost + (self.parts_cost or 0)
    
    @property
    def is_overdue(self):
        if self.scheduled_date and self.status in [MaintenanceStatus.SCHEDULED, MaintenanceStatus.IN_PROGRESS]:
            from datetime import datetime
            return datetime.now() > self.scheduled_date
        return False
    
    def to_dict(self):
        return {
            'id': self.id,
            'work_order_number': self.work_order_number,
            'car_id': self.car_id,
            'customer_id': self.customer_id,
            'technician_id': self.technician_id,
            'maintenance_type': self.maintenance_type.value,
            'status': self.status.value,
            'priority': self.priority.value,
            'service_description': self.service_description,
            'parts_needed': self.parts_needed,
            'labor_hours': self.labor_hours,
            'labor_rate': self.labor_rate,
            'parts_cost': self.parts_cost,
            'total_cost': self.total_cost,
            'scheduled_date': self.scheduled_date.isoformat() if self.scheduled_date else None,
            'started_date': self.started_date.isoformat() if self.started_date else None,
            'completed_date': self.completed_date.isoformat() if self.completed_date else None,
            'next_service_date': self.next_service_date.isoformat() if self.next_service_date else None,
            'current_mileage': self.current_mileage,
            'next_service_mileage': self.next_service_mileage,
            'notes': self.notes,
            'warranty_covered': self.warranty_covered,
            'customer_approved': self.customer_approved,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
