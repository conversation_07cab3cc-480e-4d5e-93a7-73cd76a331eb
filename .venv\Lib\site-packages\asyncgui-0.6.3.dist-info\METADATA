Metadata-Version: 2.1
Name: asyncgui
Version: 0.6.3
Summary: A thin layer that helps to wrap a callback-style API in an async/await-style API
Home-page: https://github.com/gottadiveintopython/asyncgui
License: MIT
Keywords: async
Author: <PERSON><PERSON><PERSON>
Author-email: <EMAIL>
Requires-Python: >=3.8.1,<4.0.0
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.8
Classifier: Topic :: Software Development :: Libraries
Requires-Dist: exceptiongroup (>=1.0.4,<2.0.0) ; python_version < "3.11"
Project-URL: Repository, https://github.com/gottadiveintopython/asyncgui
Description-Content-Type: text/markdown

# AsyncGui

A thin layer that helps to wrap a callback-style API in an async/await-style API.

An async library that focuses on fast reaction.

[Documentation](https://asyncgui.github.io/asyncgui/)

## Installation

Pin the minor version.

```text
poetry add asyncgui@~0.6
pip install "asyncgui>=0.6,<0.7"
```

## Tested on

- CPython 3.8
- CPython 3.9
- CPython 3.10
- CPython 3.11
- CPython 3.12 (3.12.1 or later)

## Async libraries that rely on this

- [asynckivy](https://github.com/asyncgui/asynckivy)
- [asynctkinter](https://github.com/asyncgui/asynctkinter)
- [asyncpygame](https://github.com/asyncgui/asyncpygame)

