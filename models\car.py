"""
Car model for inventory management
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, Float, Text, Enum
from sqlalchemy.sql import func
from .database import Base
import enum

class CarStatus(enum.Enum):
    AVAILABLE = "available"
    SOLD = "sold"
    RESERVED = "reserved"
    MAINTENANCE = "maintenance"

class FuelType(enum.Enum):
    GASOLINE = "gasoline"
    DIESEL = "diesel"
    HYBRID = "hybrid"
    ELECTRIC = "electric"

class Transmission(enum.Enum):
    MANUAL = "manual"
    AUTOMATIC = "automatic"
    CVT = "cvt"

class Car(Base):
    __tablename__ = "cars"
    
    id = Column(Integer, primary_key=True, index=True)
    vin = Column(String(17), unique=True, index=True, nullable=False)  # Vehicle Identification Number
    make = Column(String(50), nullable=False)  # Brand (Toyota, BMW, etc.)
    model = Column(String(50), nullable=False)  # Model name
    year = Column(Integer, nullable=False)
    color = Column(String(30), nullable=False)
    mileage = Column(Integer, default=0)  # Kilometers
    fuel_type = Column(Enum(FuelType), nullable=False)
    transmission = Column(Enum(Transmission), nullable=False)
    engine_size = Column(Float)  # Engine displacement in liters
    doors = Column(Integer, default=4)
    seats = Column(Integer, default=5)
    purchase_price = Column(Float, nullable=False)  # Cost price
    selling_price = Column(Float, nullable=False)  # Selling price
    status = Column(Enum(CarStatus), default=CarStatus.AVAILABLE, nullable=False)
    description = Column(Text)
    features = Column(Text)  # JSON string of features
    image_path = Column(String(255))  # Path to car image
    location = Column(String(100))  # Showroom location
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<Car(vin='{self.vin}', make='{self.make}', model='{self.model}', year={self.year})>"
    
    def to_dict(self):
        return {
            'id': self.id,
            'vin': self.vin,
            'make': self.make,
            'model': self.model,
            'year': self.year,
            'color': self.color,
            'mileage': self.mileage,
            'fuel_type': self.fuel_type.value,
            'transmission': self.transmission.value,
            'engine_size': self.engine_size,
            'doors': self.doors,
            'seats': self.seats,
            'purchase_price': self.purchase_price,
            'selling_price': self.selling_price,
            'status': self.status.value,
            'description': self.description,
            'features': self.features,
            'image_path': self.image_path,
            'location': self.location,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @property
    def full_name(self):
        return f"{self.year} {self.make} {self.model}"
    
    @property
    def profit_margin(self):
        if self.purchase_price and self.selling_price:
            return self.selling_price - self.purchase_price
        return 0
