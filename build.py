"""
Build script for creating executable file
سكريبت إنشاء الملف التنفيذي
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def create_spec_file():
    """Create PyInstaller spec file"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['app/main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ui/*.kv', 'ui'),
        ('assets', 'assets'),
        ('models', 'models'),
        ('services', 'services'),
        ('core', 'core'),
    ],
    hiddenimports=[
        'kivymd',
        'kivymd.app',
        'kivymd.uix.boxlayout',
        'kivymd.uix.list',
        'kivymd.uix.dialog',
        'kivymd.uix.button',
        'kivymd.uix.textfield',
        'kivymd.uix.card',
        'kivymd.uix.snackbar',
        'sqlalchemy',
        'bcrypt',
        'reportlab',
        'PIL',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CarShowroomSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/images/icon.ico' if os.path.exists('assets/images/icon.ico') else None,
)
'''
    
    with open('CarShowroomSystem.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ Spec file created: CarShowroomSystem.spec")

def build_executable():
    """Build the executable using PyInstaller"""
    print("🔨 Building executable...")
    
    try:
        # Create spec file
        create_spec_file()
        
        # Run PyInstaller
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'CarShowroomSystem.spec']
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Build successful!")
            print(f"📁 Executable created in: {os.path.abspath('dist')}")
            
            # Check if executable exists
            exe_path = Path('dist/CarShowroomSystem.exe')
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📊 File size: {size_mb:.1f} MB")
            
        else:
            print("❌ Build failed!")
            print("Error output:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error during build: {e}")
        return False
    
    return True

def clean_build_files():
    """Clean build artifacts"""
    print("🧹 Cleaning build files...")
    
    dirs_to_remove = ['build', '__pycache__']
    files_to_remove = ['CarShowroomSystem.spec']
    
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"   Removed: {dir_name}")
    
    for file_name in files_to_remove:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"   Removed: {file_name}")

def create_installer():
    """Create installer package (optional)"""
    print("📦 Creating installer package...")
    
    # This would use tools like NSIS, Inno Setup, or similar
    # For now, just create a simple batch file
    
    batch_content = '''@echo off
echo Installing Car Showroom Management System...
echo.
echo Copying files...
xcopy /E /I /Y "CarShowroomSystem.exe" "%PROGRAMFILES%\\CarShowroom\\"
echo.
echo Creating desktop shortcut...
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\\Desktop\\Car Showroom.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%PROGRAMFILES%\\CarShowroom\\CarShowroomSystem.exe" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs
cscript CreateShortcut.vbs
del CreateShortcut.vbs
echo.
echo Installation completed!
pause
'''
    
    with open('dist/install.bat', 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print("✅ Installer created: dist/install.bat")

def main():
    """Main build process"""
    print("🚗 Car Showroom Management System - Build Script")
    print("=" * 60)
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print(f"✅ PyInstaller version: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller not found. Installing...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
    
    # Create necessary directories
    os.makedirs('assets/images', exist_ok=True)
    os.makedirs('dist', exist_ok=True)
    
    # Build executable
    if build_executable():
        print("\n🎉 Build process completed successfully!")
        
        # Optional: Create installer
        create_installer()
        
        print("\n📋 Next steps:")
        print("   1. Test the executable in dist/CarShowroomSystem.exe")
        print("   2. Run dist/install.bat to install system-wide (optional)")
        print("   3. Distribute the dist folder to end users")
        
    else:
        print("\n❌ Build process failed!")
        return 1
    
    # Clean up
    clean_build_files()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
