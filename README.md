# 🚗 Car Showroom Management System

## نظام إدارة معرض السيارات المتكامل

نظام شامل ومتطور لإدارة جميع جوانب معرض السيارات باستخدام Python و KivyMD مع واجهة مستخدم عصرية وأنيقة.

## ✨ المميزات الرئيسية

### 🔐 نظام المصادقة والصلاحيات
- تسجيل دخول آمن مع تشفير كلمات المرور
- أدوار متعددة للمستخدمين (مدير، موظف مبيعات، فني صيانة)
- إدارة الصلاحيات حسب الدور

### 🚙 إدارة المخزون
- إضافة وتعديل وحذف السيارات
- تتبع حالة السيارات (متاح، مباع، محجوز، صيانة)
- بحث وفلترة متقدمة
- إدارة الصور والمواصفات التفصيلية

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- تتبع تاريخ المشتريات
- معلومات الاتصال والعناوين
- ملاحظات وتفضيلات العملاء

### 💰 إدارة المبيعات
- تسجيل عمليات البيع
- حساب الضرائب والخصومات
- إدارة طرق الدفع والتمويل
- تتبع الأرباح والعمولات

### 🔧 إدارة الصيانة
- جدولة مواعيد الصيانة
- تتبع أوامر العمل
- إدارة قطع الغيار والتكاليف
- تنبيهات الصيانة الدورية

### 📊 التقارير والإحصائيات
- تقارير المبيعات التفصيلية
- تقارير المخزون والجرد
- تحليلات العملاء
- تقارير الصيانة والخدمات

## 🛠️ التقنيات المستخدمة

- **Python 3.8+** - لغة البرمجة الأساسية
- **Kivy & KivyMD** - واجهة المستخدم الرسومية
- **SQLAlchemy** - إدارة قاعدة البيانات
- **SQLite** - قاعدة البيانات المحلية
- **bcrypt** - تشفير كلمات المرور
- **ReportLab** - توليد تقارير PDF
- **Pillow** - معالجة الصور

## 📁 هيكل المشروع

```
CarShowroomSystem/
├── app/                    # التطبيق الرئيسي
│   └── main.py            # نقطة البداية
├── core/                   # الوظائف الأساسية
│   ├── config.py          # إعدادات النظام
│   └── utils.py           # وظائف مساعدة
├── models/                 # نماذج قاعدة البيانات
│   ├── database.py        # إعداد قاعدة البيانات
│   ├── user.py           # نموذج المستخدم
│   ├── car.py            # نموذج السيارة
│   ├── customer.py       # نموذج العميل
│   ├── sale.py           # نموذج المبيعات
│   └── maintenance.py    # نموذج الصيانة
├── services/              # خدمات العمل
│   ├── auth_service.py   # خدمة المصادقة
│   ├── car_service.py    # خدمة إدارة السيارات
│   └── customer_service.py # خدمة إدارة العملاء
├── ui/                    # واجهة المستخدم
│   └── main.kv           # تصميم الواجهة
├── assets/                # الملفات المساعدة
│   ├── images/           # الصور والأيقونات
│   └── fonts/            # الخطوط
├── reports/               # التقارير المولدة
└── requirements.txt       # المتطلبات
```

## 🚀 التثبيت والتشغيل

### 1. متطلبات النظام
- Python 3.8 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

### 2. تحميل المشروع
```bash
git clone https://github.com/your-repo/car-showroom-system.git
cd car-showroom-system
```

### 3. إنشاء البيئة الافتراضية
```bash
python -m venv .venv

# Windows
.venv\Scripts\activate

# macOS/Linux
source .venv/bin/activate
```

### 4. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 5. تشغيل التطبيق
```bash
python app/main.py
```

## 👤 بيانات الدخول الافتراضية

عند التشغيل الأول، سيتم إنشاء حساب مدير افتراضي:

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

⚠️ **مهم:** يُنصح بتغيير كلمة المرور فور تسجيل الدخول الأول.

## 📱 واجهة المستخدم

### شاشة تسجيل الدخول
- تصميم أنيق مع شعار النظام
- حقول آمنة لاسم المستخدم وكلمة المرور
- إمكانية إنشاء حساب جديد

### لوحة التحكم الرئيسية
- إحصائيات سريعة (عدد السيارات، العملاء، المبيعات)
- بطاقات تفاعلية للوصول السريع
- قائمة جانبية للتنقل

### شاشات الإدارة
- قوائم تفاعلية مع إمكانية البحث والفلترة
- نوافذ حوار لإضافة وتعديل البيانات
- عرض تفصيلي للمعلومات

## 🔧 الإعدادات والتخصيص

### إعدادات قاعدة البيانات
يمكن تخصيص إعدادات قاعدة البيانات في `core/config.py`:

```python
DATABASE_URL = "sqlite:///car_showroom.db"  # SQLite محلي
# أو
DATABASE_URL = "postgresql://user:pass@localhost/dbname"  # PostgreSQL
```

### إعدادات الواجهة
```python
DEFAULT_THEME = "Dark"  # أو "Light"
PRIMARY_PALETTE = "Amber"
ACCENT_PALETTE = "Orange"
```

## 📊 إنشاء التقارير

النظام يدعم إنشاء تقارير PDF احترافية:

- تقارير المبيعات حسب التاريخ
- تقارير المخزون والجرد
- تحليلات العملاء
- تقارير الصيانة

## 🔒 الأمان

- تشفير كلمات المرور باستخدام bcrypt
- نظام صلاحيات متدرج
- حماية من SQL Injection
- تسجيل العمليات الحساسة

## 📦 إنشاء ملف تنفيذي (EXE)

لإنشاء ملف تنفيذي مستقل:

```bash
# تثبيت PyInstaller
pip install pyinstaller

# إنشاء الملف التنفيذي
pyinstaller --onefile --windowed --name="CarShowroom" app/main.py

# الملف سيكون في مجلد dist/
```

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:

- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني: <EMAIL>

## 🔄 التحديثات المستقبلية

### الإصدار 1.1 (قريباً)
- [ ] دعم قواعد بيانات متعددة
- [ ] واجهة ويب إضافية
- [ ] تطبيق موبايل مصاحب
- [ ] تكامل مع أنظمة الدفع

### الإصدار 1.2
- [ ] ذكاء اصطناعي لتوقع المبيعات
- [ ] تحليلات متقدمة
- [ ] دعم متعدد اللغات
- [ ] نظام إشعارات متطور

---

**تم تطوير هذا النظام بعناية فائقة لتوفير حل شامل ومتطور لإدارة معارض السيارات** 🚗✨
