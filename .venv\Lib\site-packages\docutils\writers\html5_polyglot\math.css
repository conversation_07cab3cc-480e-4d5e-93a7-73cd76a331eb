/*
*   math2html: convert LaTeX equations to HTML output.
*
*   Copyright (C) 2009,2010 <PERSON>
*                 2021      <PERSON><PERSON><PERSON>
*
*   Released under the terms of the `2-Clause BSD license'_, in short:
*   Copying and distribution of this file, with or without modification,
*   are permitted in any medium without royalty provided the copyright
*   notice and this notice are preserved.
*   This file is offered as-is, without any warranty.
*
* .. _2-Clause BSD license: http://www.spdx.org/licenses/BSD-2-Clause
*
*   Based on eLyXer: convert LyX source files to HTML output.
*   http://elyxer.nongnu.org/
*
*
* CSS file for LaTeX formulas.
*
* References: http://www.zipcon.net/~swhite/docs/math/math.html
*             http://www.cs.tut.fi/~jkorpela/math/
*/

/* Formulas */
.formula {
	text-align: center;
	margin: 1.2em 0;
	line-height: 1.4;
}
span.formula {
	white-space: nowrap;
}
div.formula {
	padding: 0.5ex;
	margin-left: auto;
	margin-right: auto;
}

/* Basic features */
a.eqnumber {
	display: inline-block;
	float: right;
	clear: right;
	font-weight: bold;
}
span.unknown {
	color: #800000;
}
span.ignored, span.arraydef {
	display: none;
}
.phantom {
	visibility: hidden;
}
.formula i {
	letter-spacing: 0.1ex;
}

/* Alignment */
.align-l {
	text-align: left;
}
.align-r {
	text-align: right;
}
.align-c {
	text-align: center;
}

/* Structures */
span.hspace {
	display: inline-block;
}
span.overline, span.bar {
	text-decoration: overline;
}
.fraction, .fullfraction, .textfraction {
	display: inline-block;
	vertical-align: middle;
	text-align: center;
}
span.formula .fraction,
.textfraction,
span.smallmatrix {
	font-size: 80%;
	line-height: 1;
}
span.numerator {
	display: block;
	line-height: 1;
}
span.denominator {
	display: block;
	line-height: 1;
	padding: 0ex;
	border-top: thin solid;
}
.formula sub, .formula sup {
	font-size: 80%;
}
sup.numerator, sup.unit {
	vertical-align: 80%;
}
sub.denominator, sub.unit {
	vertical-align: -20%;
}
span.smallsymbol {
	font-size: 75%;
	line-height: 75%;
}
span.boldsymbol {
	font-weight: bold;
}
span.sqrt {
	display: inline-block;
	vertical-align: middle;
	padding: 0.1ex;
}
sup.root {
	position: relative;
	left: 1.4ex;
}
span.radical {
	display: inline-block;
	padding: 0ex;
	/* font-size: 160%; for DejaVu, not required with STIX */
	line-height: 100%;
	vertical-align: top;
	vertical-align: middle;
}

span.root {
	display: inline-block;
	border-top: thin solid;
	padding: 0ex;
	vertical-align: middle;
}
div.formula .bigoperator,
.displaystyle .bigoperator,
.displaystyle .bigoperator {
	line-height: 120%;
	font-size: 140%;
	padding-right: 0.2ex;
}
span.fraction .bigoperator,
span.scriptstyle .bigoperator {
	line-height: inherit;
	font-size: inherit;
	padding-right: 0;
}
span.bigdelimiter {
	display: inline-block;
}
span.bigdelimiter.size1 {
	transform: scale(1, 1.2);
	line-height: 1.2;
}
span.bigdelimiter.size2 {
	transform: scale(1, 1.62);
	line-height: 1.62%;

}
span.bigdelimiter.size3 {
	transform: scale(1, 2.05);
	line-height: 2.05%;
}
span.bigdelimiter.size4 {
	transform: scale(1, 2.47);
	line-height: 2.47%;
}
/* vertically stacked sub and superscript */
span.scripts {
	display: inline-table;
	vertical-align: middle;
	padding-right: 0.2ex;
}
.script {
	display: table-row;
	text-align: left;
	line-height: 150%;
}
span.limits {
	display: inline-table;
	vertical-align: middle;
}
.limit {
	display: table-row;
	line-height: 99%;
}
sup.limit, sub.limit {
	line-height: 100%;
}
span.embellished,
span.embellished > .base {
	display: inline-block;
}
span.embellished > sup,
span.embellished > sub {
	display: inline-block;
	font-size: 100%;
	position: relative;
	bottom: 0.3em;
	width: 0px;
}
span.embellished > sub {
	top: 0.4em;
}

/* Environments */
span.array, span.bracketcases, span.binomial, span.environment {
	display: inline-table;
	text-align: center;
	vertical-align: middle;
}
span.arrayrow, span.binomrow {
	display: table-row;
	padding: 0;
	border: 0;
}
span.arraycell, span.bracket, span.case, span.binomcell, span.environmentcell {
	display: table-cell;
	padding: 0ex 0.2ex;
	line-height: 1; /* 99%; */
	border: 0ex;
}
.environment.align > .arrayrow > .arraycell.align-l {
	padding-right: 2em;
}

/* Inline binomials */
span.binom {
	display: inline-block;
	vertical-align: middle;
	text-align: center;
	font-size: 80%;
}
span.binomstack {
	display: block;
	padding: 0em;
}

/* Over- and underbraces */
span.overbrace {
	border-top: 2pt solid;
}
span.underbrace {
	border-bottom: 2pt solid;
}

/* Stackrel */
span.stackrel {
	display: inline-block;
	text-align: center;
}
span.upstackrel {
	display: block;
	padding: 0em;
	font-size: 80%;
	line-height: 64%;
	position: relative;
	top: 0.15em;

}
span.downstackrel {
	display: block;
	vertical-align: bottom;
	padding: 0em;
}

/* Fonts */
.formula {
	font-family: STIX, "DejaVu Serif", "DejaVu Math TeX Gyre", serif;
}
span.radical,   /* ensure correct size of square-root sign */
span.integral { /* upright integral signs for better alignment of indices */
	font-family: "STIXIntegralsUp", STIX;
	/* font-size: 115%; match apparent size with DejaVu */
}
span.bracket {
  /* some "STIX" and "DejaVu Math TeX Gyre" bracket pieces don't fit */
	font-family: "DejaVu Serif", serif;
}
span.mathsf, span.textsf {
	font-family: sans-serif;
}
span.mathrm, span.textrm {
	font-family: STIX, "DejaVu Serif", "DejaVu Math TeX Gyre", serif;
}
span.mathtt, span.texttt {
	font-family: monospace;
}
span.text, span.textnormal,
span.mathsf, span.mathtt, span.mathrm {
	font-style: normal;
}
span.fraktur {
	font-family: "Lucida Blackletter", eufm10, blackletter;
}
span.blackboard {
	font-family: Blackboard, msbm10, serif;
}
span.scriptfont {
	font-family: "Monotype Corsiva", "Apple Chancery", "URW Chancery L", cursive;
	font-style: italic;
}
span.mathscr {
  font-family: MathJax_Script, rsfs10,  cursive;
  font-style: italic;
}
span.textsc {
	font-variant: small-caps;
}
span.textsl {
	font-style: oblique;
}

/* Colors */
span.colorbox {
	display: inline-block;
	padding: 5px;
}
span.fbox {
	display: inline-block;
	border: thin solid black;
	padding: 2px;
}
span.boxed, span.framebox {
	display: inline-block;
	border: thin solid black;
	padding: 5px;
}
