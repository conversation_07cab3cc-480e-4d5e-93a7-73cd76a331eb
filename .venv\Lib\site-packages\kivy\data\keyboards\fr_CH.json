{"title": "fr_CH", "description": "A Swiss French keyboard, touch optimized (no shift+caps lock)", "cols": 15, "rows": 5, "normal_1": [["1", "1", "1", 1], ["2", "2", "2", 1], ["3", "3", "3", 1], ["4", "4", "4", 1], ["5", "5", "5", 1], ["6", "6", "6", 1], ["7", "7", "7", 1], ["8", "8", "8", 1], ["9", "9", "9", 1], ["0", "0", "0", 1], ["@", "@", "@", 1], ["?", "?", "?", 1], ["!", "!", "!", 1], ["⌫", null, "backspace", 2]], "normal_2": [["↹", "\t", "tab", 1.5], ["q", "q", "q", 1], ["w", "w", "w", 1], ["e", "e", "e", 1], ["r", "r", "r", 1], ["t", "t", "t", 1], ["z", "z", "z", 1], ["u", "u", "u", 1], ["i", "i", "i", 1], ["o", "o", "o", 1], ["p", "p", "p", 1], ["è", "è", "è", 1], [":", ":", ":", 1], ["$", "$", "$", 1.5]], "normal_3": [["⇪", null, "capslock", 1.8], ["a", "a", "a", 1], ["s", "s", "s", 1], ["d", "d", "d", 1], ["f", "f", "f", 1], ["g", "g", "g", 1], ["h", "h", "h", 1], ["j", "j", "j", 1], ["k", "k", "k", 1], ["l", "l", "l", 1], ["é", "é", "é", 1], ["à", "à", "à", 1], ["⏎", null, "enter", 2.2]], "normal_4": [["⇧", null, "shift", 2.5], ["y", "y", "y", 1], ["x", "x", "x", 1], ["c", "c", "c", 1], ["v", "v", "v", 1], ["b", "b", "b", 1], ["n", "n", "n", 1], ["m", "m", "m", 1], [",", ",", ",", 1], [".", ".", ".", 1], ["-", "-", "-", 1], ["⇧", null, "shift", 2.5]], "normal_5": [["#+=", null, "special", 2.5], [" ", " ", "spacebar", 11], ["⨯", null, "escape", 1.5]], "shift_1": [["1", "1", "1", 1], ["2", "2", "2", 1], ["3", "3", "3", 1], ["4", "4", "4", 1], ["5", "5", "5", 1], ["6", "6", "6", 1], ["7", "7", "7", 1], ["8", "8", "8", 1], ["9", "9", "9", 1], ["0", "0", "0", 1], ["@", "@", "@", 1], ["?", "?", "?", 1], ["!", "!", "!", 1], ["⌫", null, "backspace", 2]], "shift_2": [["↹", "\t", "tab", 1.5], ["Q", "Q", null, 1], ["W", "W", null, 1], ["E", "E", "e", 1], ["R", "R", "r", 1], ["T", "T", "t", 1], ["Z", "Z", "z", 1], ["U", "U", "u", 1], ["I", "I", "i", 1], ["O", "O", "o", 1], ["P", "P", "p", 1], ["È", "È", "È", 1], [":", ":", ":", 1], ["/", "/", "/", 1.5]], "shift_3": [["⇪", null, "capslock", 1.8], ["A", "A", "a", 1], ["S", "S", "s", 1], ["D", "D", "d", 1], ["F", "F", "f", 1], ["G", "G", "g", 1], ["H", "H", "h", 1], ["J", "J", "j", 1], ["K", "K", "k", 1], ["L", "L", "l", 1], ["É", "É", "É", 1], ["À", "À", "À", 1], ["⏎", null, "enter", 2.2]], "shift_4": [["⇧", null, "shift", 2.5], ["Y", "Y", "y", 1], ["X", "X", "x", 1], ["C", "C", "c", 1], ["V", "V", "v", 1], ["B", "B", "b", 1], ["N", "N", "n", 1], ["M", "M", "m", 1], [";", ";", ";", 1], [":", ":", ":", 1], ["_", "_", "_", 1], ["⇧", null, "shift", 2.5]], "shift_5": [["#+=", null, "special", 2.5], [" ", " ", "spacebar", 11], ["⨯", null, "escape", 1.5]], "special_1": [["1", "1", "1", 1], ["2", "2", "2", 1], ["3", "3", "3", 1], ["4", "4", "4", 1], ["5", "5", "5", 1], ["6", "6", "6", 1], ["7", "7", "7", 1], ["8", "8", "8", 1], ["9", "9", "9", 1], ["0", "0", "0", 1], ["@", "@", "@", 1], ["?", "?", "?", 1], ["!", "!", "!", 1], ["⌫", null, "backspace", 2]], "special_2": [["↹", "\t", "tab", 1.5], ["(", "(", "(", 1], [")", ")", ")", 1], ["{", "{", "{", 1], ["}", "}", "}", 1], ["[", "[", "[", 1], ["]", "]", "]", 1], ["€", "€", "€", 1], ["$", "$", "$", 1], ["£", "£", "£", 1], ["¥", "¥", "¥", 1], ["ü", "ü", "ü", 1], ["•", "•", "•", 1], ["|", "|", "|", 1.5]], "special_3": [["⇪", null, "capslock", 1.8], ["“", "“", "“", 1], ["`", "`", "`", 1], ["«", "«", "«", 1], ["»", "»", "»", 1], ["#", "#", "#", 1], ["%", "%", "%", 1], ["^", "^", "^", 1], ["°", "°", "°", 1], ["&", "&", "&", 1], ["ö", "ö", "ö", 1], ["ä", "ä", "ä", 1], ["⏎", null, "enter", 2.2]], "special_4": [["⇧", null, "shift", 2.5], ["+", "+", "+", 1], ["=", "=", "=", 1], ["<", "<", "<", 1], [">", ">", ">", 1], ["*", "*", "*", 1], ["Ö", "Ö", "Ö", 1], ["Ä", "Ä", "Ä", 1], ["Ü", "Ü", "Ü", 1], [":", ":", ":", 1], ["_", "_", "_", 1], ["⇧", null, "shift", 2.5]], "special_5": [["#+=", null, "special", 2.5], [" ", " ", "spacebar", 11], ["⨯", null, "escape", 1.5]]}