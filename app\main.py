
"""
Car Showroom Management System - Main Application
"""

import os
import sys
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from kivy.lang import Builder
from kivymd.app import MDApp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.list import MDListItem, MDListItemLeadingIcon, MDListItemHeadlineText
from kivymd.uix.dialog import MDDialog
from kivymd.uix.button import MDButton, MDButtonText
from kivymd.uix.textfield import MDTextField
from kivymd.uix.card import MDCard
from kivymd.uix.snackbar import MDSnackbar, MDSnackbarText
from kivy.clock import Clock

# Import services
from services.auth_service import auth_service
from services.car_service import car_service
from services.customer_service import customer_service
from models.database import init_database
from models.user import UserRole

class ContentNavigationDrawer(MDBoxLayout):
    pass

class MainApp(MDApp):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.dialog = None
        self.current_screen = "login"

    def build(self):
        # Set theme - Luxury Light Theme
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.accent_palette = "Lightblue"
        self.theme_cls.material_style = "M3"

        # Initialize database
        init_database()

        # Create default admin user if not exists
        self.create_default_admin()

        # Load UI
        return Builder.load_file('ui/main.kv')

    def on_start(self):
        # Set initial screen to login
        self.root.ids.screen_manager.current = "login"

        # Setup navigation drawer
        self.setup_navigation_drawer()

        # Update dashboard stats
        Clock.schedule_interval(self.update_dashboard_stats, 5)

    def create_default_admin(self):
        """Create default admin user if none exists"""
        try:
            result = auth_service.create_user(
                username="admin",
                email="<EMAIL>",
                full_name="System Administrator",
                password="admin123",
                role=UserRole.ADMIN
            )
            if result['success']:
                print("Default admin user created: admin/admin123")
        except Exception as e:
            print(f"Admin user might already exist: {e}")

    def setup_navigation_drawer(self):
        """Setup navigation drawer items"""
        icons_items = [
            {"icon": "view-dashboard", "text": "Dashboard", "screen": "dashboard"},
            {"icon": "car", "text": "Cars", "screen": "cars"},
            {"icon": "account-group", "text": "Customers", "screen": "customers"},
            {"icon": "cart", "text": "Sales", "screen": "sales"},
            {"icon": "cog", "text": "Maintenance", "screen": "maintenance"},
            {"icon": "chart-bar", "text": "Reports", "screen": "reports"},
        ]

        for item_data in icons_items:
            item = MDListItem(
                MDListItemLeadingIcon(icon=item_data["icon"]),
                MDListItemHeadlineText(text=item_data["text"]),
                on_release=lambda x, screen=item_data["screen"]: self.navigate_to(screen)
            )
            self.root.ids.content_drawer.ids.md_list.add_widget(item)

    def navigate_to(self, screen_name):
        """Navigate to specified screen"""
        if not auth_service.is_authenticated() and screen_name != "login":
            self.show_snackbar("Please login first")
            return

        self.root.ids.screen_manager.current = screen_name
        self.current_screen = screen_name

        # Close navigation drawer
        if hasattr(self.root, 'ids') and 'nav_drawer' in self.root.ids:
            self.root.ids.nav_drawer.set_state("close")

        # Update screen content
        if screen_name == "dashboard":
            self.update_dashboard_stats()
        elif screen_name == "cars":
            self.load_cars_list()
        elif screen_name == "customers":
            self.load_customers_list()

    def login(self):
        """Handle user login"""
        username = self.root.ids.username_field.text.strip()
        password = self.root.ids.password_field.text.strip()

        if not username or not password:
            self.show_snackbar("Please enter username and password")
            return

        result = auth_service.authenticate(username, password)

        if result['success']:
            self.show_snackbar(f"Welcome, {result['user']['full_name']}!")
            self.navigate_to("dashboard")
            # Clear login fields
            self.root.ids.username_field.text = ""
            self.root.ids.password_field.text = ""
        else:
            self.show_snackbar(result['message'])

    def logout(self):
        """Handle user logout"""
        auth_service.logout()
        self.navigate_to("login")
        self.show_snackbar("Logged out successfully")

    def show_register_dialog(self):
        """Show user registration dialog"""
        self.show_snackbar("User registration - Coming soon!")

    def show_profile(self):
        """Show user profile"""
        if auth_service.is_authenticated():
            user = auth_service.get_current_user()
            self.show_snackbar(f"Profile: {user.full_name} ({user.role.value})")
        else:
            self.show_snackbar("Please login first")

    def create_register_content(self):
        """Create registration form content"""
        content = MDBoxLayout(
            orientation="vertical",
            spacing="12dp",
            size_hint_y=None,
            height="300dp"
        )

        self.reg_username = MDTextField(hint_text="Username", mode="outlined")
        self.reg_email = MDTextField(hint_text="Email", mode="outlined")
        self.reg_full_name = MDTextField(hint_text="Full Name", mode="outlined")
        self.reg_password = MDTextField(hint_text="Password", password=True, mode="outlined")

        content.add_widget(self.reg_username)
        content.add_widget(self.reg_email)
        content.add_widget(self.reg_full_name)
        content.add_widget(self.reg_password)

        return content

    def register_user(self, *args):
        """Register new user"""
        username = self.reg_username.text.strip()
        email = self.reg_email.text.strip()
        full_name = self.reg_full_name.text.strip()
        password = self.reg_password.text.strip()

        if not all([username, email, full_name, password]):
            self.show_snackbar("Please fill all fields")
            return

        result = auth_service.create_user(username, email, full_name, password)

        if result['success']:
            self.show_snackbar("Account created successfully!")
            self.close_dialog()
        else:
            self.show_snackbar(result['message'])

    def close_dialog(self, *args):
        """Close dialog"""
        if self.dialog:
            self.dialog.dismiss()
            self.dialog = None

    def show_snackbar(self, message):
        """Show snackbar message"""
        snackbar = MDSnackbar(
            MDSnackbarText(text=message),
            y="24dp",
            pos_hint={"center_x": 0.5},
            size_hint_x=0.8,
        )
        snackbar.open()

    def update_dashboard_stats(self, *args):
        """Update dashboard statistics"""
        if not auth_service.is_authenticated() or self.current_screen != "dashboard":
            return

        try:
            # Get car stats
            car_stats = car_service.get_inventory_stats()
            customer_stats = customer_service.get_customer_stats()

            # Update UI
            if hasattr(self.root.ids, 'total_cars_label'):
                self.root.ids.total_cars_label.text = str(car_stats.get('total_cars', 0))
            if hasattr(self.root.ids, 'total_customers_label'):
                self.root.ids.total_customers_label.text = str(customer_stats.get('total_customers', 0))
            if hasattr(self.root.ids, 'total_sales_label'):
                self.root.ids.total_sales_label.text = "0"  # TODO: Implement sales stats
            if hasattr(self.root.ids, 'total_revenue_label'):
                self.root.ids.total_revenue_label.text = f"${car_stats.get('total_inventory_value', 0):,.0f}"

        except Exception as e:
            print(f"Error updating dashboard stats: {e}")

    def load_cars_list(self):
        """Load cars list"""
        try:
            cars = car_service.get_all_cars()
            cars_list = self.root.ids.cars_list
            cars_list.clear_widgets()

            for car in cars:
                item = MDListItem(
                    MDListItemLeadingIcon(icon="car"),
                    MDListItemHeadlineText(text=f"{car.full_name} - ${car.selling_price:,.0f}"),
                    on_release=lambda x, car_id=car.id: self.show_car_details(car_id)
                )
                cars_list.add_widget(item)

        except Exception as e:
            self.show_snackbar(f"Error loading cars: {e}")

    def load_customers_list(self):
        """Load customers list"""
        try:
            customers = customer_service.get_all_customers()
            customers_list = self.root.ids.customers_list
            customers_list.clear_widgets()

            for customer in customers:
                item = MDListItem(
                    MDListItemLeadingIcon(icon="account"),
                    MDListItemHeadlineText(text=f"{customer.full_name} - {customer.phone}"),
                    on_release=lambda x, customer_id=customer.id: self.show_customer_details(customer_id)
                )
                customers_list.add_widget(item)

        except Exception as e:
            self.show_snackbar(f"Error loading customers: {e}")

    def search_cars(self, search_text):
        """Search cars"""
        if not search_text.strip():
            self.load_cars_list()
            return

        try:
            cars = car_service.search_cars(search_text)
            cars_list = self.root.ids.cars_list
            cars_list.clear_widgets()

            for car in cars:
                item = MDListItem(
                    MDListItemLeadingIcon(icon="car"),
                    MDListItemHeadlineText(text=f"{car.full_name} - ${car.selling_price:,.0f}"),
                    on_release=lambda x, car_id=car.id: self.show_car_details(car_id)
                )
                cars_list.add_widget(item)

        except Exception as e:
            self.show_snackbar(f"Error searching cars: {e}")

    def search_customers(self, search_text):
        """Search customers"""
        if not search_text.strip():
            self.load_customers_list()
            return

        try:
            customers = customer_service.search_customers(search_text)
            customers_list = self.root.ids.customers_list
            customers_list.clear_widgets()

            for customer in customers:
                item = MDListItem(
                    MDListItemLeadingIcon(icon="account"),
                    MDListItemHeadlineText(text=f"{customer.full_name} - {customer.phone}"),
                    on_release=lambda x, customer_id=customer.id: self.show_customer_details(customer_id)
                )
                customers_list.add_widget(item)

        except Exception as e:
            self.show_snackbar(f"Error searching customers: {e}")

    # Placeholder methods for dialog functions
    def show_add_car_dialog(self):
        self.show_snackbar("Add Car dialog - Coming soon!")

    def show_add_customer_dialog(self):
        self.show_snackbar("Add Customer dialog - Coming soon!")

    def show_add_sale_dialog(self):
        self.show_snackbar("Add Sale dialog - Coming soon!")

    def show_add_maintenance_dialog(self):
        self.show_snackbar("Add Maintenance dialog - Coming soon!")

    def show_car_filter_dialog(self):
        self.show_snackbar("Car Filter dialog - Coming soon!")

    def show_date_filter_dialog(self):
        self.show_snackbar("Date Filter dialog - Coming soon!")

    def show_car_details(self, car_id):
        self.show_snackbar(f"Car details for ID: {car_id} - Coming soon!")

    def show_customer_details(self, customer_id):
        self.show_snackbar(f"Customer details for ID: {customer_id} - Coming soon!")

    def search_sales(self, search_text):
        self.show_snackbar("Sales search - Coming soon!")

    def search_maintenance(self, search_text):
        self.show_snackbar("Maintenance search - Coming soon!")

    # Report generation methods
    def generate_sales_report(self):
        self.show_snackbar("Sales report generation - Coming soon!")

    def generate_inventory_report(self):
        self.show_snackbar("Inventory report generation - Coming soon!")

    def generate_customer_report(self):
        self.show_snackbar("Customer report generation - Coming soon!")

    def generate_maintenance_report(self):
        self.show_snackbar("Maintenance report generation - Coming soon!")

if __name__ == '__main__':
    MainApp().run()
