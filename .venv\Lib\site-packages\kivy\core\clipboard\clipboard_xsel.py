'''
Clipboard xsel: an implementation of the Clipboard using xsel command line
                tool.
'''

__all__ = ('ClipboardXsel', )

from kivy.utils import platform
from kivy.core.clipboard._clipboard_ext import ClipboardExternalBase

if platform != 'linux':
    raise SystemError('unsupported platform for xsel clipboard')

import subprocess
p = subprocess.Popen(['xsel', '--version'], stdout=subprocess.PIPE)
p.communicate(timeout=1)


class ClipboardXsel(ClipboardExternalBase):
    @staticmethod
    def _clip(inout, selection):
        pipe = {'std' + inout: subprocess.PIPE}
        sel = 'b' if selection == 'clipboard' else selection[0]
        io = inout[0]
        return subprocess.<PERSON>n(
            ['xsel', '-' + sel + io], **pipe)
