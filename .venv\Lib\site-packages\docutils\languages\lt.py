# $Id: lt.py 9030 2022-03-05 23:28:32Z milde $
# Author: <PERSON><PERSON> <<EMAIL>>
# Copyright: This module has been placed in the public domain.

# New language mappings are welcome.  Before doing a new translation, please
# read <https://docutils.sourceforge.io/docs/howto/i18n.html>.
# Two files must be translated for each language: one in docutils/languages,
# the other in docutils/parsers/rst/languages.

"""
Lithuanian language mappings for language-dependent features of Docutils.
"""

__docformat__ = 'reStructuredText'

labels = {
      # fixed: language-dependent
      'author': '<PERSON><PERSON>',
      'authors': 'Autoriai',
      'organization': 'Organizacija',
      'address': '<PERSON><PERSON><PERSON>',
      'contact': '<PERSON>nta<PERSON><PERSON>',
      'version': 'Versija',
      'revision': 'Revizija',
      'status': 'Būsena',
      'date': 'Data',
      'copyright': 'Autoriaus teis<PERSON>',
      'dedication': '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      'abstract': '<PERSON><PERSON><PERSON>',
      'attention': '<PERSON><PERSON><PERSON><PERSON>!',
      'caution': 'Atsar<PERSON>i!',
      'danger': '!PAVOJINGA!',
      'error': '<PERSON>laid<PERSON>',
      'hint': 'Užuomina',
      'important': 'Svarbu',
      'note': 'Pastaba',
      'tip': 'Patarimas',
      'warning': 'Įspėjimas',
      'contents': 'Turinys'}
"""Mapping of node class name to label text."""

bibliographic_fields = {
      # language-dependent: fixed
      'autorius': 'author',
      'autoriai': 'authors',
      'organizacija': 'organization',
      'adresas': 'address',
      'kontaktas': 'contact',
      'versija': 'version',
      'revizija': 'revision',
      'būsena': 'status',
      'data': 'date',
      'autoriaus teisės': 'copyright',
      'dedikacija': 'dedication',
      'santrauka': 'abstract'}
"""Lithuanian (lowcased) to canonical name mapping for bibliographic fields."""

author_separators = [';', ',']
"""List of separator strings for the 'Authors' bibliographic field. Tried in
order."""
