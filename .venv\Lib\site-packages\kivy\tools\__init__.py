"""
Tools
=====

The tools module provides various utility scripts, modules and examples.

Scripts
-------

Some useful scripts include:

* :file:`kviewer.py`: for viewing kv files with automatic updating
* :file:`benchmark.py`: provides detailed OpenGL hardware information as well
  as some benchmarks measuring kivy specific performance
* :file:`reports.py`: provides a comprehensive report covering your systems
  providers, libraries, configuration, environment, input devices and options
* :file:`texturecompress.py`: a command line utility for compressing images
  into PVRTC or ETC1 formats
* :file:`generate-icons.py`: generates set of icons suitable for the various
  store and package formats
* :file:`gles_compat/subset_gles.py`: examines compatibility between GLEXT and
  GLES2 headers for finding compatible subsets

Modules
-------

Tool modules provide various resources for:

* :mod:`~kivy.tools.packaging`
* :mod:`text editor highlighting <kivy.tools.highlight>`


Other
-----

Other miscellaneous resources include

* :file:`pep8checker`: pep8 checking scripts and git hook
* :file:`theming`: demonstrates an alternative theme for kivy
* :file:`travis`: travis continuous integration

This help document is a work-in-progress and currently under construction.

"""
