#!/usr/bin/env python3
"""
Car Showroom Management System - Launcher
نظام إدارة معرض السيارات - ملف التشغيل
"""

import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def main():
    """Main entry point for the application"""
    try:
        print("🚗 Car Showroom Management System")
        print("=" * 50)
        print("Starting application...")
        
        # Import and run the main application
        from app.main import MainApp
        
        app = MainApp()
        app.run()
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n💡 Please make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
