"""
Installation and setup script for Car Showroom Management System
سكريبت التثبيت والإعداد لنظام إدارة معرض السيارات
"""

import os
import sys
import subprocess
import venv
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required!")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def create_virtual_environment():
    """Create virtual environment"""
    venv_path = Path(".venv")
    
    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return True
    
    print("🔧 Creating virtual environment...")
    try:
        venv.create(".venv", with_pip=True)
        print("✅ Virtual environment created successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to create virtual environment: {e}")
        return False

def get_venv_python():
    """Get path to Python executable in virtual environment"""
    if os.name == 'nt':  # Windows
        return Path(".venv/Scripts/python.exe")
    else:  # Unix/Linux/macOS
        return Path(".venv/bin/python")

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    
    python_exe = get_venv_python()
    
    if not python_exe.exists():
        print("❌ Virtual environment Python not found")
        return False
    
    try:
        # Upgrade pip first
        subprocess.run([str(python_exe), "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # Install requirements
        subprocess.run([str(python_exe), "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        
        print("✅ All packages installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("📁 Creating necessary directories...")
    
    directories = [
        "assets/images",
        "assets/fonts",
        "assets/uploads",
        "reports/generated",
        "backups",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   Created: {directory}")
    
    print("✅ Directories created successfully")

def create_desktop_shortcut():
    """Create desktop shortcut (Windows only)"""
    if os.name != 'nt':
        return
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "Car Showroom.lnk")
        target = os.path.join(os.getcwd(), "run.py")
        wDir = os.getcwd()
        icon = os.path.join(os.getcwd(), "assets", "images", "icon.ico")
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = wDir
        if os.path.exists(icon):
            shortcut.IconLocation = icon
        shortcut.save()
        
        print("✅ Desktop shortcut created")
        
    except ImportError:
        print("⚠️  Desktop shortcut creation skipped (winshell not available)")
    except Exception as e:
        print(f"⚠️  Could not create desktop shortcut: {e}")

def test_installation():
    """Test if installation was successful"""
    print("🧪 Testing installation...")
    
    python_exe = get_venv_python()
    
    try:
        # Test importing main modules
        test_script = '''
import sys
sys.path.append(".")

try:
    from models.database import init_database
    from services.auth_service import auth_service
    from services.car_service import car_service
    print("✅ All modules imported successfully")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
'''
        
        result = subprocess.run([str(python_exe), "-c", test_script], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Installation test passed")
            return True
        else:
            print(f"❌ Installation test failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

def print_usage_instructions():
    """Print usage instructions"""
    print("\n" + "="*60)
    print("🎉 Installation completed successfully!")
    print("="*60)
    
    print("\n📋 How to run the application:")
    
    if os.name == 'nt':  # Windows
        print("   1. Double-click 'run.py' file")
        print("   2. Or run in command prompt:")
        print("      .venv\\Scripts\\python.exe run.py")
    else:  # Unix/Linux/macOS
        print("   1. Run in terminal:")
        print("      .venv/bin/python run.py")
    
    print("\n🔐 Default login credentials:")
    print("   Username: admin")
    print("   Password: admin123")
    print("   (Please change password after first login)")
    
    print("\n📚 Additional commands:")
    print("   Build executable: python build.py")
    print("   Run tests: python -m pytest (if installed)")
    
    print("\n💡 For help and documentation:")
    print("   Check README.md file")
    print("   Visit: https://github.com/your-repo/car-showroom-system")

def main():
    """Main installation process"""
    print("🚗 Car Showroom Management System - Installation")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Create virtual environment
    if not create_virtual_environment():
        return 1
    
    # Install requirements
    if not install_requirements():
        return 1
    
    # Create directories
    create_directories()
    
    # Create desktop shortcut (Windows only)
    create_desktop_shortcut()
    
    # Test installation
    if not test_installation():
        print("⚠️  Installation completed with warnings")
    
    # Print usage instructions
    print_usage_instructions()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
